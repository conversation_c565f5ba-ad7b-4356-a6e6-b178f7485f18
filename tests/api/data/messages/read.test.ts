// @ts-ignore
import { describe, it, expect, beforeEach } from "bun:test"
import { ConversationMessageBusinessLogicInterface } from "@/lib/repositories/conversationMessages/interface"
import { ConversationMessageBusinessLogic } from "@/lib/repositories/conversationMessages/BusinessLogic"
import { MongoConversationMessageRepository } from "@/lib/repositories/conversationMessages/MongoRepository"
import { TestConversationMessageDBRepositoryWrapper } from "./TestDBRepositoryWrapper"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"
import {
  implHandleCreateConversationMessage,
  implHandleGetConversationMessage,
  implHandleGetAllConversationMessages,
} from "@/app/api/v1/conversations/[conversationId]/messages/impl"
import {
  createConversationMessage,
  createSimpleConversationMessages,
  createConversationMessagesWithTags,
  createSearchByNameParams,
  createSearchByTagParams,
  createUnmatchedSearchParams,
  createEmptySearchParams,
  createWhitespaceSearchParams,
  createUndefinedSearchParams,
  createCustomerTagFilterParams,
  createVipTagFilterParams,
  createNonExistentFilterParams,
  createEmptyFilterFieldParams,
  createWhitespaceFilterFieldParams,
} from "./object_creator"

describe("Read ConversationMessage API Tests", () => {
  let businessLogic: ConversationMessageBusinessLogicInterface
  let dbRepository: TestConversationMessageDBRepositoryWrapper

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("ConversationMessage")
    await driver.connect()
    const originalDb = new MongoConversationMessageRepository(driver)
    dbRepository = new TestConversationMessageDBRepositoryWrapper(
      originalDb,
      driver,
    )
    businessLogic = new ConversationMessageBusinessLogic(dbRepository)
    await dbRepository.clear()
  })

  describe("GET /api/v1/conversations/[conversationId]/messages/:id", () => {
    it("should successfully get conversationMessage by ID", async () => {
      const conversationMessage = createConversationMessage(5) // JConversationDoe ConversationMessage

      const createResult = await implHandleCreateConversationMessage(
        conversationMessage,
        businessLogic,
      )
      const id = createResult.body.data.id

      const result = await implHandleGetConversationMessage(id, businessLogic)
      expect(result.status).toBe(200)
      expect(result.body.data?.id).toBe(id)
      expect(result.body.data?.STRING_FIELD).toBe(
        conversationMessage.STRING_FIELD,
      )
    })

    it("should fail to get non-existent conversationMessage", async () => {
      const result = await implHandleGetConversationMessage(
        "507f1f77bcf86cd799439011",
        businessLogic,
      )
      expect(result.status).toBe(404)
    })

    it("should fail with empty conversationMessage ID", async () => {
      const result = await implHandleGetConversationMessage("", businessLogic)
      expect(result.status).toBe(400)
    })

    it("should fail with whitespace-only conversationMessage ID", async () => {
      const result = await implHandleGetConversationMessage(
        "   ",
        businessLogic,
      )
      expect(result.status).toBe(400)
    })
  })

  describe("GET /api/v1/conversations/[conversationId]/messages", () => {
    it("should get all conversationMessages", async () => {
      const conversationMessages = creaConversationmpleConversationMessages()
      for (const r of conversationMessages)
        await implHandleCreateConversationMessage(r, businessLogic)

      const result = await implHandleGetAllConversationMessages(businessLogic)
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(3)
    })

    it("should return empty when no conversationMessages exist", async () => {
      const result = await implHandleGetAllConversationMessages(businessLogic)
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(0)
    })
  })

  describe("GET /api/v1/conversations/[conversationId]/messages/search", () => {
    beforeEach(async () => {
      const data = createConversationMessagesWithTags()
      for (const r of data)
        await implHandleCreateConversationMessage(r, businessLogic)
    })

    it("should search by STRING_FIELD", async () => {
      const params = createSearchByNameParams()
      const result = await implHandleGetAllConversationMessages(
        businessLogic,
        params,
      )
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(2)
    })

    it("should search by tag", async () => {
      const params = createSearchByTagParams()
      const result = await implHandleGetAllConversationMessages(
        businessLogic,
        params,
      )
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(2)
    })

    it("should return empty for unmatched search", async () => {
      const params = createUnmatchedSearchParams()
      const result = await implHandleGetAllConversationMessages(
        businessLogic,
        params,
      )
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(0)
    })

    it("should reject empty search keyword", async () => {
      const params = createEmptySearchParams()
      const result = await implHandleGetAllConversationMessages(
        businessLogic,
        params,
      )
      expect(result.status).toBe(400)
    })

    it("should reject whitespace-only search keyword", async () => {
      const params = createWhitespaceSearchParams()
      const result = await implHandleGetAllConversationMessages(
        businessLogic,
        params,
      )
      expect(result.status).toBe(400)
    })

    it("should return all if search is undefined", async () => {
      const params = createUndefinedSearchParams()
      const result = await implHandleGetAllConversationMessages(
        businessLogic,
        params,
      )
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(4)
    })
  })

  describe("GET /api/v1/conversations/[conversationId]/messages/filters", () => {
    beforeEach(async () => {
      const data = createConversationMessagesWithTags()
      for (const r of data)
        await implHandleCreateConversationMessage(r, businessLogic)
    })

    it("should filter by tag 'Customer'", async () => {
      const params = createCustomerTagFilterParams()
      const result = await implHandleGetAllConversationMessages(
        businessLogic,
        params,
      )
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(2)
    })

    it("should filter by tag 'VIP'", async () => {
      const params = createVipTagFilterParams()
      const result = await implHandleGetAllConversationMessages(
        businessLogic,
        params,
      )
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(2)
    })

    it("should return empty for non-existent tag", async () => {
      const params = createNonExistentFilterParams()
      const result = await implHandleGetAllConversationMessages(
        businessLogic,
        params,
      )
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(0)
    })

    it("should reject empty filter field", async () => {
      const params = createEmptyFilterFieldParams()
      const result = await implHandleGetAllConversationMessages(
        businessLogic,
        params,
      )
      expect(result.status).toBe(400)
    })

    it("should reject whitespace-only filter field", async () => {
      const params = createWhitespaceFilterFieldParams()
      const result = await implHandleGetAllConversationMessages(
        businessLogic,
        params,
      )
      expect(result.status).toBe(400)
    })
  })
})
