# Build stage
FROM oven/bun:1.2.10-alpine AS builder

# Accept version as build argument
ARG APP_VERSION=development
ENV NEXT_PUBLIC_APP_VERSION=$APP_VERSION

WORKDIR /app

# Copy package files
COPY package.json bun.lock ./

# Install dependencies
RUN bun install --frozen-lockfile

# Copy source code for building
COPY . .

# APP_VERSION will be passed by the Makefile.
# Use make build_image to build the image
ENV NEXT_PUBLIC_APP_VERSION=$APP_VERSION

# Build the application
RUN bun run build

# Production stage
FROM oven/bun:1.2.10-alpine AS runner

WORKDIR /app

# Create non-root user
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Copy package files for production dependencies
COPY package.json bun.lock ./

# Install only production dependencies
RUN bun install --frozen-lockfile --production

# Copy built application from builder stage
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static
COPY --from=builder --chown=nextjs:nodejs /app/public ./public

# Set environment variables
ENV NODE_ENV=production
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

# Switch to non-root user
USER nextjs

EXPOSE 3000

# Start the application
CMD ["node", "server.js"]
