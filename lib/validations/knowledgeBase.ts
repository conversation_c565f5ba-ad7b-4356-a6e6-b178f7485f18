import { z } from "zod"

export const KnowledgeBaseCreateSchema = z.object({
  content: z.string().min(1, "Content is required"),
  editor: z.object({
    rawJson: z.string().min(1, "Editor content is required"),
  }),
})

export const KnowledgeBaseUpdateSchema = z.object({
  content: z.string().min(1, "Content is required").optional(),
  editor: z
    .object({
      rawJson: z.string().min(1, "Editor content is required"),
    })
    .optional(),
})

export const KnowledgeBaseDocumentCreateSchema = z.object({
  name: z
    .string()
    .min(1, "Name is required")
    .max(200, "Name must be less than 200 characters"),
  originalName: z.string().min(1, "Original name is required"),
  size: z.number().min(0, "Size must be non-negative"),
  mimeType: z.string().min(1, "MIME type is required"),
  filePath: z.string().min(1, "File path is required"),
  extractedContent: z.string().optional(),
  keywords: z.array(z.string()).optional(),
  isProcessed: z.boolean().optional().default(false),
  isActive: z.boolean().optional().default(true),
  // Note: createdBy and organizationId will be added from SessionContext
})

export const KnowledgeBaseDocumentUpdateSchema = z.object({
  name: z
    .string()
    .min(1, "Name is required")
    .max(200, "Name must be less than 200 characters")
    .optional(),
  extractedContent: z.string().optional(),
  keywords: z.array(z.string()).optional(),
  isProcessed: z.boolean().optional(),
  isActive: z.boolean().optional(),
  // Note: updatedBy will be added from SessionContext
})

export const KnowledgeBaseQuerySchema = z.object({
  search: z.string().optional(),
  page: z.number().min(1).optional().default(1),
  limit: z.number().min(1).max(100).optional().default(10),
  includeDeleted: z.boolean().optional().default(false),
  category: z.string().optional(),
  tags: z.array(z.string()).optional(),
  isActive: z.boolean().optional(),
  sortBy: z
    .enum(["title", "createdAt", "updatedAt"])
    .optional()
    .default("createdAt"),
  sortOrder: z.enum(["ASC", "DESC"]).optional().default("DESC"),
})

export const KnowledgeBaseDocumentQuerySchema = z.object({
  search: z.string().optional(),
  page: z.number().min(1).optional().default(1),
  limit: z.number().min(1).max(100).optional().default(10),
  includeDeleted: z.boolean().optional().default(false),
  isProcessed: z.boolean().optional(),
  isActive: z.boolean().optional(),
  mimeType: z.string().optional(),
  sortBy: z
    .enum(["name", "size", "createdAt", "updatedAt"])
    .optional()
    .default("createdAt"),
  sortOrder: z.enum(["ASC", "DESC"]).optional().default("DESC"),
})

export const KnowledgeBaseSearchSchema = z.object({
  query: z.string().min(1, "Search query is required"),
  limit: z.number().min(1).max(50).optional().default(10),
})

export const KnowledgeBaseProcessDocumentSchema = z.object({
  documentId: z.string().min(1, "Document ID is required"),
})

export const KnowledgeBaseExtractKeywordsSchema = z.object({
  content: z.string().min(1, "Content is required"),
})

export const KnowledgeBaseUpdateContentSchema = z.object({
  content: z.string().min(1, "Content is required"),
})

export const KnowledgeBaseParseContentSchema = z.object({
  knowledgeBaseId: z.string().min(1, "Knowledge base ID is required"),
})

// Bulk operation schemas
export const KnowledgeBaseBulkCreateSchema = z.object({
  items: z
    .array(KnowledgeBaseCreateSchema)
    .min(1, "At least one item is required"),
})

export const KnowledgeBaseBulkUpdateSchema = z.object({
  updates: z
    .array(
      z.object({
        id: z.string().min(1, "ID is required"),
        data: KnowledgeBaseUpdateSchema,
      }),
    )
    .min(1, "At least one update is required"),
})

export const KnowledgeBaseBulkDeleteSchema = z.object({
  ids: z
    .array(z.string().min(1, "ID cannot be empty"))
    .min(1, "At least one ID is required"),
  hardDelete: z.boolean().optional().default(false),
})

// File upload schema
export const KnowledgeBaseFileUploadSchema = z.object({
  file: z.any(), // File object
  name: z.string().optional(),
  category: z.string().optional(),
  tags: z.array(z.string()).optional(),
  autoProcess: z.boolean().optional().default(true),
})

export type KnowledgeBaseCreateInput = z.infer<typeof KnowledgeBaseCreateSchema>
export type KnowledgeBaseUpdateInput = z.infer<typeof KnowledgeBaseUpdateSchema>
export type KnowledgeBaseDocumentCreateInput = z.infer<
  typeof KnowledgeBaseDocumentCreateSchema
>
export type KnowledgeBaseDocumentUpdateInput = z.infer<
  typeof KnowledgeBaseDocumentUpdateSchema
>
export type KnowledgeBaseQueryInput = z.infer<typeof KnowledgeBaseQuerySchema>
export type KnowledgeBaseDocumentQueryInput = z.infer<
  typeof KnowledgeBaseDocumentQuerySchema
>
export type KnowledgeBaseSearchInput = z.infer<typeof KnowledgeBaseSearchSchema>
export type KnowledgeBaseBulkCreateInput = z.infer<
  typeof KnowledgeBaseBulkCreateSchema
>
export type KnowledgeBaseBulkUpdateInput = z.infer<
  typeof KnowledgeBaseBulkUpdateSchema
>
export type KnowledgeBaseBulkDeleteInput = z.infer<
  typeof KnowledgeBaseBulkDeleteSchema
>
export type KnowledgeBaseFileUploadInput = z.infer<
  typeof KnowledgeBaseFileUploadSchema
>
