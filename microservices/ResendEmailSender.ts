import { Resend } from "resend"
import { EmailSender } from "./EmailSender"
import { locales } from "@/lib/repositories/email-locales"
import { localize } from "@/localization/functions/server"

const APP_BASE_URL = process.env.NEXT_PUBLIC_APP_BASE_URL!
const ACCOUNT_FROM_EMAIL = `CSPintar Account <account@${process.env.EMAIL_DOMAIN_SEND!}>`

let _resend: Resend | null = null

function getResend(): Resend {
  if (!_resend) {
    if (!process.env.RESEND_API_KEY) {
      throw new Error("Missing RESEND_API_KEY environment variable.")
    }
    _resend = new Resend(process.env.RESEND_API_KEY)
  }
  return _resend
}

export class ResendEmailSender implements EmailSender {
  async sendEmailVerification(email: string, token: string): Promise<void> {
    const { t } = await localize("email-sender", locales)
    const url = `${APP_BASE_URL}/auth/verify-email?token=${token}`

    await getResend().emails.send({
      from: ACCOUNT_FROM_EMAIL,
      to: email,
      subject: t("verify_subject"),
      html: `
        <p style="font-family: sans-serif; font-size: 16px;">
          ${t("verify_html", { url })}
        </p>
        <p>${t("verify_ignore")}</p>
      `,
      text: `${t("verify_text", { url })}\n\n${t("verify_ignore")}`,
    })
  }

  async sendPasswordReset(email: string, token: string): Promise<void> {
    const { t } = await localize("email-sender", locales)
    const url = `${APP_BASE_URL}/auth/reset-password?token=${token}`

    await getResend().emails.send({
      from: ACCOUNT_FROM_EMAIL,
      to: email,
      subject: t("reset_subject"),
      html: `
        <p style="font-family: sans-serif; font-size: 16px;">
          ${t("reset_html", { url })}
        </p>
        <p>${t("reset_ignore")}</p>
      `,
      text: `${t("reset_text", { url })}\n\n${t("reset_ignore")}`,
    })
  }
}
