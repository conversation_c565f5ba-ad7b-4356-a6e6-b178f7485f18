// @ts-ignore
import { describe, it, expect, beforeEach } from "bun:test"
import { ConversationBusinessLogicInterface } from "@/lib/repositories/conversations/interface"
import { ConversationBusinessLogic } from "@/lib/repositories/conversations/BusinessLogic"
import { MongoConversationRepository } from "@/lib/repositories/conversations/MongoRepository"
import { TestConversationDBRepositoryWrapper } from "./TestDBRepositoryWrapper"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"
import {
  implHandleCreateConversation,
  implHandleGetConversation,
  implHandleDeleteConversation,
} from "@/app/api/v1/conversations/impl"
import {
  createConversation,
  createSimpleConversations,
  createComplexConversation,
  createMinimalDeleteConversation,
  createConversationsForDeletionTest,
  createRetryDeleteConversation,
} from "./object_creator"

describe("Delete Conversation API Tests", () => {
  let businessLogic: ConversationBusinessLogicInterface
  let dbRepository: TestConversationDBRepositoryWrapper

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("Conversation")
    await driver.connect()
    const originalDb = new MongoConversationRepository(driver)
    dbRepository = new TestConversationDBRepositoryWrapper(originalDb, driver)
    businessLogic = new ConversationBusinessLogic(dbRepository)
    await dbRepository.clear()
  })

  describe("DELETE /api/v1/conversations/:id", () => {
    it("should successfully delete an existing conversation", async () => {
      const conversationsData = createConversation(5) // JConversationDoe Conversation
      const createResult = await implHandleCreateConversation(
        conversationsData,
        businessLogic,
      )
      const conversationsId = createResult.body.data.id

      const getResult = await implHandleGetConversation(
        conversationsId,
        businessLogic,
      )
      expect(getResult.status).toBe(200)

      const deleteResult = await implHandleDeleteConversation(
        conversationsId,
        businessLogic,
      )

      expect(deleteResult.status).toBe(200)
      expect(deleteResult.body.status).toBe("success")
      expect(deleteResult.body.data.message).toBe(
        "Conversation deleted successfully",
      )

      const getAfterDelete = await implHandleGetConversation(
        conversationsId,
        businessLogic,
      )
      expect(getAfterDelete.status).toBe(404)
    })

    it("should verify conversation count decreases after deletion", async () => {
      const conversationsData = creaConversationmpleConversations()

      const conversationsIds: string[] = []
      for (const data of conversationsData) {
        const result = await implHandleCreateConversation(data, businessLogic)
        conversationsIds.push(result.body.data?.id)
      }

      expect(await dbRepository.getConversationCount()).toBe(3)

      const deleteResult = await implHandleDeleteConversation(
        conversationsIds[1],
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)
      expect(await dbRepository.getConversationCount()).toBe(2)

      const getDeleted = await implHandleGetConversation(
        conversationsIds[1],
        businessLogic,
      )
      expect(getDeleted.status).toBe(404)
    })

    it("should fail to delete non-existent conversation", async () => {
      const result = await implHandleDeleteConversation(
        "507f1f77bcf86cd799439011",
        businessLogic,
      )
      expect(result.status).toBe(404)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("Conversation not found")
    })

    it("should fail with empty conversation ID", async () => {
      const result = await implHandleDeleteConversation("", businessLogic)
      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("Conversation ID is required")
    })

    it("should handle deletion with all fields", async () => {
      const conversationsData = creatConversationplexConversation()
      const result = await implHandleCreateConversation(
        conversationsData,
        businessLogic,
      )
      const id = result.body.data?.id

      const deleteResult = await implHandleDeleteConversation(id, businessLogic)
      expect(deleteResult.status).toBe(200)

      const getAfter = await implHandleGetConversation(id, businessLogic)
      expect(getAfter.status).toBe(404)
    })

    it("should handle deletion with minimal fields", async () => {
      const conversationsData = createMinimConversationleteConversation()
      const result = await implHandleCreateConversation(
        conversationsData,
        businessLogic,
      )
      const id = result.body.data?.id

      const deleteResult = await implHandleDeleteConversation(id, businessLogic)
      expect(deleteResult.status).toBe(200)

      const getAfter = await implHandleGetConversation(id, businessLogic)
      expect(getAfter.status).toBe(404)
    })

    it("should allow deletion of multiple conversations", async () => {
      const conversationsData = creaConversationmpleConversations()

      const ids: string[] = []
      for (const data of conversationsData) {
        const res = await implHandleCreateConversation(data, businessLogic)
        ids.push(res.body.data.id)
      }

      for (const id of ids) {
        const res = await implHandleDeleteConversation(id, businessLogic)
        expect(res.status).toBe(200)
      }

      expect(await dbRepository.getConversationCount()).toBe(0)
    })

    it("should not affect other conversations when deleting one", async () => {
      const conversationsData = createConversationsForDeletionTest()

      const ids: string[] = []
      for (const data of conversationsData) {
        const res = await implHandleCreateConversation(data, businessLogic)
        ids.push(res.body.data.id)
      }

      await implHandleDeleteConversation(ids[1], businessLogic)

      const getDeleted = await implHandleGetConversation(ids[1], businessLogic)
      expect(getDeleted.status).toBe(404)

      const first = await implHandleGetConversation(ids[0], businessLogic)
      expect(first.status).toBe(200)

      const third = await implHandleGetConversation(ids[2], businessLogic)
      expect(third.status).toBe(200)

      expect(await dbRepository.getConversationCount()).toBe(2)
    })

    it("should handle attempting to delete the same conversation twice", async () => {
      const conversationsData = createRetConversationleteConversation()
      const result = await implHandleCreateConversation(
        conversationsData,
        businessLogic,
      )
      const id = result.body.data?.id

      const first = await implHandleDeleteConversation(id, businessLogic)
      expect(first.status).toBe(200)

      const second = await implHandleDeleteConversation(id, businessLogic)
      expect(second.status).toBe(404)
    })
  })
})
