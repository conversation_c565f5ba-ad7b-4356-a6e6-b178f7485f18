//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test"
import { ConversationBusinessLogicInterface } from "@/lib/repositories/conversations/interface"
import { ConversationBusinessLogic } from "@/lib/repositories/conversations/BusinessLogic"
import { MongoConversationRepository } from "@/lib/repositories/conversations/MongoRepository"
import { TestConversationDBRepositoryWrapper } from "./TestDBRepositoryWrapper"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"
import {
  implHandleCreateConversation,
  implHandleGetConversation,
  implHandleBulkCreateConversations,
  implHandleBulkUpdateConversations,
  implHandleBulkDeleteConversations,
} from "@/app/api/v1/conversations/impl"
import {
  createMultipleConversations,
  createSimpleConversations,
  createExistingConversation,
  createDuplicateConversationsForBulk,
  createConversationsForBulkUpdate,
  createBulkUpdateData,
  createConversationsForBulkDelete,
} from "./object_creator"

describe("Conversation Bulk Operations Tests", () => {
  let businessLogic: ConversationBusinessLogicInterface
  let dbRepository: TestConversationDBRepositoryWrapper

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("Conversation")
    await driver.connect()
    const originalDb = new MongoConversationRepository(driver)
    dbRepository = new TestConversationDBRepositoryWrapper(originalDb, driver)
    businessLogic = new ConversationBusinessLogic(dbRepository)
    await dbRepository.clear()
  })

  describe("Bulk Create", () => {
    it("should successfully create multiple conversations", async () => {
      const conversationsData = createConversationipleConversations()

      const result = await implHandleBulkCreateConversations(
        conversationsData,
        businessLogic,
      )

      expect(result.status).toBe(201)
      expect(result.body.status).toBe("success")
      expect(result.body.data).toHaveLength(conversationsData.length)
      expect(result.body.data[0].STRING_FIELD).toBe(
        conversationsData[0].STRING_FIELD,
      )
      expect(result.body.data[1].STRING_FIELD).toBe(
        conversationsData[1].STRING_FIELD,
      )
      expect(result.body.data[2].STRING_FIELD).toBe(
        conversationsData[2].STRING_FIELD,
      )
      expect(await dbRepository.getConversationCount()).toBe(
        conversationsData.length,
      )
    })

    it("should fail if any conversation has duplicate STRING_FIELD", async () => {
      const existingConversation = createExistingConversation()
      const createResult = await implHandleCreateConversation(
        existingConversation,
        businessLogic,
      )
      expect(createResult.status).toBe(201)

      const conversationsData = createDConversationcateConversationsForBulk()

      const result = await implHandleBulkCreateConversations(
        conversationsData,
        businessLogic,
      )

      expect(result.status).toBe(409)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain(
        "Duplicate STRING_FIELD found: Existing Conversation",
      )
      expect(await dbRepository.getConversationCount()).toBe(1)
    })

    it("should handle simple conversations creation", async () => {
      const conversationsData = creaConversationmpleConversations()

      const result = await implHandleBulkCreateConversations(
        conversationsData,
        businessLogic,
      )

      expect(result.status).toBe(201)
      expect(result.body.status).toBe("success")
      expect(result.body.data).toHaveLength(conversationsData.length)
      expect(await dbRepository.getConversationCount()).toBe(
        conversationsData.length,
      )
    })
  })

  describe("Bulk Update", () => {
    it("should successfully update multiple conversations", async () => {
      const conversationsData = createConversationsForBulkUpdate()
      const createResult1 = await implHandleCreateConversation(
        conversationsData[0],
        businessLogic,
      )
      const createResult2 = await implHandleCreateConversation(
        conversationsData[1],
        businessLogic,
      )
      expect(createResult1.status).toBe(201)
      expect(createResult2.status).toBe(201)

      const updateData = createBulkUpdateData()
      const updates = [
        { id: createResult1.body.data.id, data: updateData[0] },
        { id: createResult2.body.data.id, data: updateData[1] },
      ]

      const result = await implHandleBulkUpdateConversations(
        updates,
        businessLogic,
      )

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data.updatedCount).toBe(2)

      const getResult1 = await implHandleGetConversation(
        createResult1.body.data.id,
        businessLogic,
      )
      const getResult2 = await implHandleGetConversation(
        createResult2.body.data.id,
        businessLogic,
      )

      expect(getResult1.body.data?.STRING_FIELD).toBe(
        updateData[0].STRING_FIELD,
      )
      expect(getResult1.body.data?.updatedBy).toBe(updateData[0].updatedBy)
      expect(getResult2.body.data?.STRING_FIELD).toBe(
        updateData[1].STRING_FIELD,
      )
      expect(getResult2.body.data?.updatedBy).toBe(updateData[1].updatedBy)
    })

    it("should fail if any conversation doesn't exist", async () => {
      const conversationData = createConversationsForBulkUpdate()[0]
      const createResult = await implHandleCreateConversation(
        conversationData,
        businessLogic,
      )
      expect(createResult.status).toBe(201)

      const updateData = createBulkUpdateData()
      const updates = [
        { id: createResult.body.data.id, data: updateData[0] },
        { id: "non-existent-id", data: updateData[1] },
      ]

      const result = await implHandleBulkUpdateConversations(
        updates,
        businessLogic,
      )

      expect(result.status).toBe(500)
      expect(result.body.status).toBe("failed")
    })

    it("should fail if any update would create duplicate STRING_FIELD", async () => {
      const conversationsData = createConversationsForBulkUpdate()
      const createResult1 = await implHandleCreateConversation(
        conversationsData[0],
        businessLogic,
      )
      const createResult2 = await implHandleCreateConversation(
        conversationsData[1],
        businessLogic,
      )
      expect(createResult1.status).toBe(201)
      expect(createResult2.status).toBe(201)

      const updates = [
        {
          id: createResult2.body.data.id,
          data: {
            STRING_FIELD: conversationsData[0].STRING_FIELD,
            updatedBy: "admin",
          }, // Try to update second conversation with first conversation's STRING_FIELD
        },
      ]

      const result = await implHandleBulkUpdateConversations(
        updates,
        businessLogic,
      )

      expect(result.status).toBe(409)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain(
        "Duplicate STRING_FIELD in update: Conversation 1",
      )
    })
  })

  describe("Bulk Delete", () => {
    it("should successfully soft delete multiple conversations", async () => {
      const conversationsData = createConversationsForBulkDelete()
      const createResult1 = await implHandleCreateConversation(
        conversationsData[0],
        businessLogic,
      )
      const createResult2 = await implHandleCreateConversation(
        conversationsData[1],
        businessLogic,
      )
      const createResult3 = await implHandleCreateConversation(
        conversationsData[2],
        businessLogic,
      )
      expect(createResult1.status).toBe(201)
      expect(createResult2.status).toBe(201)
      expect(createResult3.status).toBe(201)

      const result = await implHandleBulkDeleteConversations(
        [createResult1.body.data.id, createResult2.body.data.id],
        businessLogic,
      )

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data.deletedCount).toBe(2)
      expect(await dbRepository.getConversationCount()).toBe(1) // Only non-deleted

      const getResult1 = await implHandleGetConversation(
        createResult1.body.data.id,
        businessLogic,
      )
      const getResult2 = await implHandleGetConversation(
        createResult2.body.data.id,
        businessLogic,
      )
      const getResult3 = await implHandleGetConversation(
        createResult3.body.data.id,
        businessLogic,
      )

      expect(getResult1.status).toBe(404)
      expect(getResult2.status).toBe(404)
      expect(getResult3.status).toBe(200)
    })

    it("should successfully hard delete multiple conversations", async () => {
      const conversationsData = createConversationsForBulkDelete()
      const createResult1 = await implHandleCreateConversation(
        conversationsData[0],
        businessLogic,
      )
      const createResult2 = await implHandleCreateConversation(
        conversationsData[1],
        businessLogic,
      )
      expect(createResult1.status).toBe(201)
      expect(createResult2.status).toBe(201)

      const result = await implHandleBulkDeleteConversations(
        [createResult1.body.data.id, createResult2.body.data.id],
        businessLogic,
        true,
      )

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data.deletedCount).toBe(2)
      expect(await dbRepository.getConversationCount()).toBe(0)
    })

    it("should fail if any conversation doesn't exist", async () => {
      const conversationData = createConversationsForBulkDelete()[0]
      const createResult = await implHandleCreateConversation(
        conversationData,
        businessLogic,
      )
      expect(createResult.status).toBe(201)

      const result = await implHandleBulkDeleteConversations(
        [createResult.body.data.id, "non-existent-id"],
        businessLogic,
      )

      expect(result.status).toBe(404)
      expect(result.body.status).toBe("failed")
    })

    it("should fail with empty conversation IDs", async () => {
      const result = await implHandleBulkDeleteConversations(
        ["", "valid-id"],
        businessLogic,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("ID at index 0 is required")
    })
  })
})
