# Variables
PROJECT_NAME := $(shell basename $(PWD))
GIT_COMMIT := $(shell git rev-parse --short HEAD 2>/dev/null || echo "unknown")
GIT_BRANCH := $(shell git rev-parse --abbrev-ref HEAD 2>/dev/null || echo "unknown")
APP_VERSION := $(shell git tag --list --sort=-version:refname | head -1 2>/dev/null || echo "v0.0.0")
IMAGE_TAG := $(PROJECT_NAME):$(APP_VERSION)
LATEST_TAG := $(PROJECT_NAME):latest

# Colors for output
BLUE := \033[0;34m
GREEN := \033[0;32m
YELLOW := \033[1;33m
RED := \033[0;31m
CYAN := \033[0;36m
BOLD := \033[1m
NC := \033[0m

# Help target
.PHONY: help
help:
	@printf "$(CYAN)$(BOLD)CS AI App Deployment & Management$(NC)\n"
	@echo ""
	@printf "$(BOLD)USAGE:$(NC)\n"
	@echo "  make <target> [options]"
	@echo ""
	@printf "$(BOLD)BUILD & DEPLOY TARGETS:$(NC)\n"
	@echo "  build_image                     - Build new Docker image with version tag"
	@echo "  local_stg_redeploy              - Build and deploy to staging"
	@echo "  local_prod_redeploy             - Build and deploy to production"
	@echo "  local_stg_redeploy_nobuild      - Deploy existing image to staging"
	@echo "  local_prod_redeploy_nobuild     - Deploy existing image to production"
	@echo ""
	@printf "$(BOLD)MANAGEMENT TARGETS:$(NC)\n"
	@echo "  list_images                     - List all available Docker images"
	@echo "  show_current_image              - Show current git and image information"
	@echo "  show_status                     - Show comprehensive deployment status"
	@echo "  show_logs_staging               - Show staging application logs"
	@echo "  show_logs_production            - Show production application logs"
	@echo "  cleanup_old_images              - Remove old images (keeps latest 5)"
	@echo ""
	@printf "$(BOLD)ROLLBACK TARGETS:$(NC)\n"
	@echo "  rollback_staging TAG=<tag>      - Rollback staging to specific image"
	@echo "  rollback_production TAG=<tag>   - Rollback production to specific image"
	@echo ""
	@printf "$(BOLD)UTILITY TARGETS:$(NC)\n"
	@echo "  list_test_scenario              - List test scenario titles from test files"
	@echo ""
	@printf "$(BOLD)CURRENT PROJECT INFO:$(NC)\n"
	@echo "  Project: $(PROJECT_NAME)"
	@echo "  Git Commit: $(GIT_COMMIT)"
	@echo "  Git Branch: $(GIT_BRANCH)"
	@echo "  App Version: $(APP_VERSION)"
	@echo "  Would build as: $(IMAGE_TAG)"
	@echo "  Latest tag: $(LATEST_TAG)"
	@echo ""
	@printf "$(BOLD)EXAMPLES:$(NC)\n"
	@echo "  make build_image                # Build new image"
	@echo "  make local_stg_redeploy         # Build and deploy to staging"
	@echo "  make local_prod_redeploy_nobuild # Deploy existing image to production"
	@echo "  make rollback_staging TAG=cs-ai-app:abc123f  # Rollback staging"
	@echo "  make show_status                # Check deployment status"

# List test scenario titles from test() or it() in the tests directory
.PHONY: list_test_scenario
list_test_scenario:
	grep -R -E 'test\(|it\(' tests/ \
	| sed -E 's/.*(test|it)\("([^"]+)".*/\2/' \
	| sort | uniq

# Build and tag image with version tag
.PHONY: build_image
build_image:
	@printf "$(BLUE)[INFO]$(NC) Bumping version and building Docker image...\n"
	@./scripts/bump-version.sh
	@NEW_VERSION=$$(git tag --list --sort=-version:refname | head -1); \
	NEW_IMAGE_TAG="$(PROJECT_NAME):$$NEW_VERSION"; \
	printf "$(BLUE)[INFO]$(NC) Git commit: $(GIT_COMMIT)\n"; \
	printf "$(BLUE)[INFO]$(NC) Git branch: $(GIT_BRANCH)\n"; \
	printf "$(BLUE)[INFO]$(NC) App version: $$NEW_VERSION\n"; \
	printf "$(BLUE)[INFO]$(NC) Image tag: $$NEW_IMAGE_TAG\n"; \
	docker build --build-arg APP_VERSION=$$NEW_VERSION -t $$NEW_IMAGE_TAG -t $(LATEST_TAG) .; \
	printf "$(GREEN)[SUCCESS]$(NC) Image built successfully: $$NEW_IMAGE_TAG\n"; \
	printf "$(BLUE)[INFO]$(NC) Also tagged as: $(LATEST_TAG)\n"

# Redeploy to local staging (with build)
.PHONY: local_stg_redeploy
local_stg_redeploy:
	@$(MAKE) build_image
	@echo "$(BLUE)[INFO]$(NC) Deploying to staging environment..."
	@CS_AI_APP_IMAGE=$(LATEST_TAG) bash docker-start.sh -d
	@echo "$(GREEN)[SUCCESS]$(NC) Deployment to staging completed!"
	@echo ""
	@echo "$(BLUE)[INFO]$(NC) Application is running:"
	@port=$$(grep DOCKER_EXPOSED_PORT .env 2>/dev/null | cut -d'=' -f2 || echo "3000"); \
	echo "  URL: http://localhost:$$port"; \
	echo "  Environment: staging"

# Redeploy to local production (with build)
.PHONY: local_prod_redeploy
local_prod_redeploy:
	@$(MAKE) build_image
	@echo "$(BLUE)[INFO]$(NC) Deploying to production environment..."
	@CS_AI_APP_IMAGE=$(LATEST_TAG) bash docker-start.sh -p -d
	@echo "$(GREEN)[SUCCESS]$(NC) Deployment to production completed!"
	@echo ""
	@echo "$(BLUE)[INFO]$(NC) Application is running:"
	@port=$$(grep DOCKER_EXPOSED_PORT .env 2>/dev/null | cut -d'=' -f2 || echo "3000"); \
	echo "  URL: http://localhost:$$port"; \
	echo "  Environment: production"

# Redeploy to local staging (no build - use latest image)
.PHONY: local_stg_redeploy_nobuild
local_stg_redeploy_nobuild:
	@echo "$(BLUE)[INFO]$(NC) Deploying to staging environment..."
	@if ! docker image inspect $(LATEST_TAG) >/dev/null 2>&1; then \
		echo -e "$(RED)[ERROR]$(NC) No latest image found. Run 'make local_stg_redeploy' first."; \
		exit 1; \
	fi
	@echo "$(BLUE)[INFO]$(NC) Using existing image: $(LATEST_TAG)"
	@CS_AI_APP_IMAGE=$(LATEST_TAG) bash docker-start.sh -d
	@echo "$(GREEN)[SUCCESS]$(NC) Deployment to staging completed!"
	@echo ""
	@echo "$(BLUE)[INFO]$(NC) Application is running:"
	@port=$$(grep DOCKER_EXPOSED_PORT .env 2>/dev/null | cut -d'=' -f2 || echo "3000"); \
	echo "  URL: http://localhost:$$port"; \
	echo "  Environment: staging"

# Redeploy to local production (no build - use latest image)
.PHONY: local_prod_redeploy_nobuild
local_prod_redeploy_nobuild:
	@echo "$(BLUE)[INFO]$(NC) Deploying to production environment..."
	@if ! docker image inspect $(LATEST_TAG) >/dev/null 2>&1; then \
		echo -e "$(RED)[ERROR]$(NC) No latest image found. Run 'make local_prod_redeploy' first."; \
		exit 1; \
	fi
	@echo "$(BLUE)[INFO]$(NC) Using existing image: $(LATEST_TAG)"
	@CS_AI_APP_IMAGE=$(LATEST_TAG) bash docker-start.sh -p -d
	@echo "$(GREEN)[SUCCESS]$(NC) Deployment to production completed!"
	@echo ""
	@echo "$(BLUE)[INFO]$(NC) Application is running:"
	@port=$$(grep DOCKER_EXPOSED_PORT .env 2>/dev/null | cut -d'=' -f2 || echo "3000"); \
	echo "  URL: http://localhost:$$port"; \
	echo "  Environment: production"

# List all project images
.PHONY: list_images
list_images:
	@printf "$(BLUE)[INFO]$(NC) Available Docker images:\n"
	@if docker images --filter "reference=$(PROJECT_NAME)" --format "table {{.Repository}}:{{.Tag}}\t{{.CreatedAt}}\t{{.Size}}" | grep -q "$(PROJECT_NAME)"; then \
		docker images --filter "reference=$(PROJECT_NAME)" --format "table {{.Repository}}:{{.Tag}}\t{{.CreatedAt}}\t{{.Size}}"; \
	else \
		printf "$(YELLOW)[WARNING]$(NC) No images found for project: $(PROJECT_NAME)\n"; \
		echo "Run 'make build_image' to create the first image"; \
	fi

# Show current image info
.PHONY: show_current_image
show_current_image:
	@printf "$(BLUE)[INFO]$(NC) Current Git Information:\n"
	@echo "  Commit: $(GIT_COMMIT)"
	@echo "  Branch: $(GIT_BRANCH)"
	@echo "  Would build as: $(IMAGE_TAG)"
	@echo "  Latest tag: $(LATEST_TAG)"

# Show comprehensive deployment status
.PHONY: show_status
show_status:
	@printf "$(CYAN)$(BOLD)Current Project Status$(NC)\n"
	@echo ""
	@printf "$(BLUE)[INFO]$(NC) Git Information:\n"
	@echo "  Commit: $(GIT_COMMIT)"
	@echo "  Branch: $(GIT_BRANCH)"
	@echo "  Would build as: $(IMAGE_TAG)"
	@echo ""
	@printf "$(BLUE)[INFO]$(NC) Docker Images:\n"
	@$(MAKE) list_images
	@echo ""
	@printf "$(BLUE)[INFO]$(NC) Running Containers:\n"
	@staging_running=$$(docker compose -f docker-compose.yml ps -q 2>/dev/null | wc -l); \
	prod_running=$$(docker compose -f docker-compose.prod.yml ps -q 2>/dev/null | wc -l); \
	if [ "$$staging_running" -gt 0 ]; then \
		echo "  ✅ Staging: Running ($$staging_running containers)"; \
		docker compose -f docker-compose.yml ps --format "table {{.Name}}\t{{.Status}}\t{{.Ports}}" 2>/dev/null || true; \
	else \
		echo "  ❌ Staging: Not running"; \
	fi; \
	echo ""; \
	if [ "$$prod_running" -gt 0 ]; then \
		echo "  ✅ Production: Running ($$prod_running containers)"; \
		docker compose -f docker-compose.prod.yml ps --format "table {{.Name}}\t{{.Status}}\t{{.Ports}}" 2>/dev/null || true; \
	else \
		echo "  ❌ Production: Not running"; \
	fi

# Show staging logs
.PHONY: show_logs_staging
show_logs_staging:
	@echo "$(BLUE)[INFO]$(NC) Showing logs for staging environment..."
	@echo "$(BLUE)[INFO]$(NC) Press Ctrl+C to exit log view"
	@echo ""
	@if docker compose -f docker-compose.yml ps -q | grep -q .; then \
		docker compose -f docker-compose.yml logs -f; \
	else \
		echo -e "$(YELLOW)[WARNING]$(NC) No containers running for staging environment"; \
		echo "Run 'make local_stg_redeploy' to start the application"; \
	fi

# Show production logs
.PHONY: show_logs_production
show_logs_production:
	@echo "$(BLUE)[INFO]$(NC) Showing logs for production environment..."
	@echo "$(BLUE)[INFO]$(NC) Press Ctrl+C to exit log view"
	@echo ""
	@if docker compose -f docker-compose.prod.yml ps -q | grep -q .; then \
		docker compose -f docker-compose.prod.yml logs -f; \
	else \
		echo -e "$(YELLOW)[WARNING]$(NC) No containers running for production environment"; \
		echo "Run 'make local_prod_redeploy' to start the application"; \
	fi

# Rollback staging to specific image
.PHONY: rollback_staging
rollback_staging:
	@if [ -z "$(TAG)" ]; then \
		echo -e "$(RED)[ERROR]$(NC) TAG parameter is required"; \
		echo "Usage: make rollback_staging TAG=<image-tag>"; \
		echo ""; \
		echo "Available images:"; \
		$(MAKE) list_images; \
		exit 1; \
	fi
	@if ! docker image inspect "$(TAG)" >/dev/null 2>&1; then \
		echo -e "$(RED)[ERROR]$(NC) Image '$(TAG)' not found!"; \
		echo ""; \
		echo "Available images:"; \
		$(MAKE) list_images; \
		exit 1; \
	fi
	@echo "$(YELLOW)[WARNING]$(NC) You are about to rollback staging to image: $(TAG)"
	@read -p "Are you sure? (y/N): " confirm && [ "$$confirm" = "y" ] || [ "$$confirm" = "Y" ] || (echo "Rollback cancelled" && exit 1)
	@echo "$(BLUE)[INFO]$(NC) Rolling back staging to $(TAG)..."
	@CS_AI_APP_IMAGE="$(TAG)" bash docker-start.sh -d
	@echo "$(GREEN)[SUCCESS]$(NC) Rollback completed!"
	@echo ""
	@echo "$(BLUE)[INFO]$(NC) Application is running:"
	@port=$$(grep DOCKER_EXPOSED_PORT .env 2>/dev/null | cut -d'=' -f2 || echo "3000"); \
	echo "  URL: http://localhost:$$port"; \
	echo "  Environment: staging"

# Rollback production to specific image
.PHONY: rollback_production
rollback_production:
	@if [ -z "$(TAG)" ]; then \
		echo -e "$(RED)[ERROR]$(NC) TAG parameter is required"; \
		echo "Usage: make rollback_production TAG=<image-tag>"; \
		echo ""; \
		echo "Available images:"; \
		$(MAKE) list_images; \
		exit 1; \
	fi
	@if ! docker image inspect "$(TAG)" >/dev/null 2>&1; then \
		echo -e "$(RED)[ERROR]$(NC) Image '$(TAG)' not found!"; \
		echo ""; \
		echo "Available images:"; \
		$(MAKE) list_images; \
		exit 1; \
	fi
	@echo "$(YELLOW)[WARNING]$(NC) You are about to rollback production to image: $(TAG)"
	@read -p "Are you sure? (y/N): " confirm && [ "$$confirm" = "y" ] || [ "$$confirm" = "Y" ] || (echo "Rollback cancelled" && exit 1)
	@echo "$(BLUE)[INFO]$(NC) Rolling back production to $(TAG)..."
	@CS_AI_APP_IMAGE="$(TAG)" bash docker-start.sh -p -d
	@echo "$(GREEN)[SUCCESS]$(NC) Rollback completed!"
	@echo ""
	@echo "$(BLUE)[INFO]$(NC) Application is running:"
	@port=$$(grep DOCKER_EXPOSED_PORT .env 2>/dev/null | cut -d'=' -f2 || echo "3000"); \
	echo "  URL: http://localhost:$$port"; \
	echo "  Environment: production"

# Cleanup old images (keeps latest 5)
.PHONY: cleanup_old_images
cleanup_old_images:
	@echo "$(BLUE)[INFO]$(NC) Cleaning up old Docker images..."
	@echo "$(YELLOW)[WARNING]$(NC) This will remove all but the latest 5 images for $(PROJECT_NAME)"
	@read -p "Continue? (y/N): " confirm && [ "$$confirm" = "y" ] || [ "$$confirm" = "Y" ] || (echo "Cleanup cancelled" && exit 0)
	@images_to_remove=$$(docker images --filter "reference=$(PROJECT_NAME)" --format "{{.ID}}" | tail -n +6); \
	if [ -n "$$images_to_remove" ]; then \
		echo "$$images_to_remove" | xargs docker rmi -f; \
		echo -e "$(GREEN)[SUCCESS]$(NC) Cleanup completed"; \
	else \
		echo -e "$(BLUE)[INFO]$(NC) No old images to remove"; \
	fi
