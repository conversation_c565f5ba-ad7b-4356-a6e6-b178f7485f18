//@ts-ignore
import { beforeEach, describe, expect, it, test } from "bun:test"
import { implHandleRegister } from "@/app/api/v1/auth/register/impl"
import { MongoAuthDBRepository } from "@/lib/repositories/auth"
import { AuthBusinessLogicInterface } from "@/lib/repositories/auth/AuthBusinessLogicInterface"
import { AuthBusinessLogic } from "@/lib/repositories/auth/BusinessLogic"
import type { UserRegister } from "@/lib/types/base"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"
import { TestAuthDBRepositoryWrapper } from "./TestDBRepositoryWrapper"

describe("Register API Tests", () => {
  let businessLogic: AuthBusinessLogicInterface
  let dbRepository: TestAuthDBRepositoryWrapper

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("Auth")
    await driver.connect()
    const originalDb = new MongoAuthDBRepository(driver)
    dbRepository = new TestAuthDBRepositoryWrapper(originalDb, driver)
    businessLogic = new AuthBusinessLogic(dbRepository)
    await dbRepository.clear()
  })

  describe("POST /api/v1/auth/register", () => {
    it("should successfully register a new user", async () => {
      const userData: UserRegister = {
        email: "<EMAIL>",
        password: "password123",
        name: "New User",
      }

      const result = await implHandleRegister(userData, businessLogic)

      // Verify API response format
      expect(result.status).toBe(201)
      expect(result.body.status).toBe("success")
      expect(result.body?.data).toBeDefined()
      expect(result.body?.data.token).toBeDefined()
      expect(result.body?.data.refresh_token).toBeDefined()
      expect(result.body?.data.user).toBeDefined()

      // Verify user data in response
      expect(result.body?.data.user.email).toBe("<EMAIL>")
      expect(result.body?.data.user.name).toBe("New User")
      expect(result.body?.data.user.id).toBeDefined()
      expect(result.body?.data.user.createdAt).toBeDefined()
      expect(result.body?.data.user.updatedAt).toBeDefined()
      expect(result.body?.data.user.emailVerified).toBe(false)

      // Verify data persistence in repository
      expect(await dbRepository.getUserCount()).toBe(1)
      expect(await dbRepository.getTokenCount()).toBe(1)
      expect(await dbRepository.getEmailVerificationCount()).toBe(1)

      // Verify token is valid
      const validatedUser = await businessLogic.validateToken(
        result.body?.data.token,
      )
      expect(validatedUser).toBeTruthy()
      expect(validatedUser?.email).toBe("<EMAIL>")
    })

    it("should reject duplicate email registration", async () => {
      const userData: UserRegister = {
        email: "<EMAIL>",
        password: "password123",
        name: "First User",
      }

      // Register first user
      const result1 = await implHandleRegister(userData, businessLogic)
      expect(result1.status).toBe(201)

      // Try to register with same email
      const userData2: UserRegister = {
        email: "<EMAIL>",
        password: "differentpassword",
        name: "Second User",
      }

      const result2 = await implHandleRegister(userData2, businessLogic)

      expect(result2.status).toBe(409)
      expect(result2.body.status).toBe("failed")
      expect(result2.body.error).toContain(
        "User with this email already exists",
      )
      expect(await dbRepository.getUserCount()).toBe(1) // Only first user should exist
    })

    it("should fail registration with missing email", async () => {
      const userData = {
        password: "password123",
        name: "Test User",
      } as UserRegister

      const result = await implHandleRegister(userData, businessLogic)

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.errorCodes).toContain("ERROR_VALIDATION_FAILED")
    })

    it("should fail registration with missing password", async () => {
      const userData = {
        email: "<EMAIL>",
        name: "Test User",
      } as UserRegister

      const result = await implHandleRegister(userData, businessLogic)

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.errorCodes).toContain("ERROR_VALIDATION_FAILED")
    })

    it("should handle registration with missing name", async () => {
      const userData = {
        email: "<EMAIL>",
        password: "password123",
      } as UserRegister

      const result = await implHandleRegister(userData, businessLogic)

      // Current implementation doesn't validate name field
      expect(result.status).toBe(201)
      expect(result.body.status).toBe("success")
      expect(result.body?.data.user.name).toBe("")
    })

    it("should fail registration with invalid email format", async () => {
      const userData: UserRegister = {
        email: "invalid-email",
        password: "password123",
        name: "Test User",
      }

      const result = await implHandleRegister(userData, businessLogic)

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.errorCodes).toContain("ERROR_VALIDATION_FAILED")
    })

    it("should fail registration with weak password", async () => {
      const userData: UserRegister = {
        email: "<EMAIL>",
        password: "123", // Too short
        name: "Test User",
      }

      const result = await implHandleRegister(userData, businessLogic)

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.errorCodes).toContain("ERROR_VALIDATION_FAILED")
    })

    it("should fail registration with empty fields", async () => {
      const userData: UserRegister = {
        email: "",
        password: "",
        name: "",
      }

      const result = await implHandleRegister(userData, businessLogic)

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.errorCodes).toContain("ERROR_VALIDATION_FAILED")
    })

    it("should handle case-sensitive email registration", async () => {
      const userData1: UserRegister = {
        email: "<EMAIL>",
        password: "password123",
        name: "Test User 1",
      }

      const result1 = await implHandleRegister(userData1, businessLogic)
      expect(result1.status).toBe(201)

      // Try to register with same email in different case
      const userData2: UserRegister = {
        email: "<EMAIL>",
        password: "password456",
        name: "Test User 2",
      }

      const result2 = await implHandleRegister(userData2, businessLogic)

      // Current implementation treats emails as case-sensitive
      expect(result2.status).toBe(201)
      expect(result2.body.status).toBe("success")
      expect(result2.body.data.user.email).toBe("<EMAIL>")
    })

    it("should handle whitespace in email and name", async () => {
      const userData: UserRegister = {
        email: "  <EMAIL>  ",
        password: "password123",
        name: "  Test User  ",
      }

      const result = await implHandleRegister(userData, businessLogic)

      // Current implementation doesn't trim whitespace
      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(Array.isArray(result.body.error)).toBe(true)
    })
  })
})
