//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test"
import { ConversationMessageBusinessLogicInterface } from "@/lib/repositories/conversationMessages/interface"
import { ConversationMessageBusinessLogic } from "@/lib/repositories/conversationMessages/BusinessLogic"
import { MongoConversationMessageRepository } from "@/lib/repositories/conversationMessages/MongoRepository"
import { TestConversationMessageDBRepositoryWrapper } from "./TestDBRepositoryWrapper"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"
import {
  implHandleCreateConversationMessage,
  implHandleGetConversationMessage,
  implHandleDeleteConversationMessage,
  implHandleUpdateConversationMessage,
  implHandleGetAllConversationMessages,
  implHandleRestoreConversationMessage,
} from "@/app/api/v1/conversations/[conversationId]/messages/impl"
import {
  createConversationMessage,
  createConversationMessageUpdate,
  createConversationMessageWithDescription,
  createTestConversationMessage,
  createTestConversationMessage2,
} from "./object_creator"

describe("ConversationMessage Soft Delete Tests", () => {
  let businessLogic: ConversationMessageBusinessLogicInterface
  let dbRepository: TestConversationMessageDBRepositoryWrapper

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("ConversationMessage")
    await driver.connect()
    const originalDb = new MongoConversationMessageRepository(driver)
    dbRepository = new TestConversationMessageDBRepositoryWrapper(
      originalDb,
      driver,
    )
    businessLogic = new ConversationMessageBusinessLogic(dbRepository)
    await dbRepository.clear()
  })

  describe("Soft Delete", () => {
    it("should soft delete a conversationMessages by default", async () => {
      const conversationMessageData = createConversationMessageWithDescription()
      const createResult = await implHandleCreateConversationMessage(
        conversationMessageData,
        businessLogic,
      )
      expect(createResult.status).toBe(201)

      const deleteResult = await implHandleDeleteConversationMessage(
        createResult.body.data.id,
        businessLogic,
      )

      expect(deleteResult.status).toBe(200)
      expect(deleteResult.body.status).toBe("success")

      // ConversationMessage should not be accessible by default
      const getResult = await implHandleGetConversationMessage(
        createResult.body.data.id,
        businessLogic,
      )
      expect(getResult.status).toBe(404)

      // But should be accessible when including deleted
      const getDeletedResult = await implHandleGetConversationMessage(
        createResult.body.data.id,
        businessLogic,
        true,
      )
      expect(getDeletedResult.status).toBe(200)
      expect(getDeletedResult.body.data).not.toBeNull()

      // Count should exclude soft deleted
      expect(await dbRepository.getConversationMessageCount()).toBe(0)
      expect(await dbRepository.getConversationMessageCount(true)).toBe(1)
    })

    it("should hard delete when specified", async () => {
      const conversationMessageData = createConversationMessageWithDescription()
      const createResult = await implHandleCreateConversationMessage(
        conversationMessageData,
        businessLogic,
      )
      expect(createResult.status).toBe(201)

      // Hard delete using impl function
      const deleteResult = await implHandleDeleteConversationMessage(
        createResult.body.data.id,
        businessLogic,
        true,
      )

      expect(deleteResult.status).toBe(200)
      expect(deleteResult.body.status).toBe("success")

      // ConversationMessage should not be accessible at all
      const getResult = await implHandleGetConversationMessage(
        createResult.body.data.id,
        businessLogic,
      )
      expect(getResult.status).toBe(404)
      const getDeletedResult = await implHandleGetConversationMessage(
        createResult.body.data.id,
        businessLogic,
        true,
      )
      expect(getDeletedResult.status).toBe(404)

      // Count should be 0 in both cases
      expect(await dbRepository.getConversationMessageCount()).toBe(0)
      expect(await dbRepository.getConversationMessageCount(true)).toBe(0)
    })

    it("should not include soft deleted conversationMessages in getAll by default", async () => {
      const conversationMessageData1 = createConversationMessage(1)
      const conversationMessageData2 = createConversationMessage(2)

      const createResult1 = await implHandleCreateConversationMessage(
        conversationMessageData1,
        businessLogic,
      )
      const createResult2 = await implHandleCreateConversationMessage(
        conversationMessageData2,
        businessLogic,
      )
      expect(createResult1.status).toBe(201)
      expect(createResult2.status).toBe(201)

      // Soft delete one conversationMessages
      const deleteResult = await implHandleDeleteConversationMessage(
        createResult1.body.data.id,
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)

      const result = await implHandleGetAllConversationMessages(businessLogic)

      expect(result.status).toBe(200)
      expect(result.body.data?.items).toHaveLength(1)
      expect(result.body.data?.total).toBe(1)
      expect(result.body.data?.items[0].id).toBe(createResult2.body.data.id)
    })

    it("should include soft deleted conversationMessages when specified", async () => {
      const conversationMessageData1 = createConversationMessage(1)
      const conversationMessageData2 = createConversationMessage(2)

      const createResult1 = await implHandleCreateConversationMessage(
        conversationMessageData1,
        businessLogic,
      )
      const createResult2 = await implHandleCreateConversationMessage(
        conversationMessageData2,
        businessLogic,
      )
      expect(createResult1.status).toBe(201)
      expect(createResult2.status).toBe(201)

      // Soft delete one conversationMessages
      const deleteResult = await implHandleDeleteConversationMessage(
        createResult1.body.data.id,
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)

      const result = await implHandleGetAllConversationMessages(businessLogic, {
        includeDeleted: true,
      })

      expect(result.status).toBe(200)
      expect(result.body.data?.items).toHaveLength(2)
      expect(result.body.data?.total).toBe(2)

      const deletedConversationMessage = result.body.data?.items.find(
        (c: any) => c.id === createResult1.body.data.id,
      )
      expect(deletedConversationMessage).toBeDefined()
      expect(deletedConversationMessage?.deletedAt).toBeDefined()
    })

    it("should not allow updating soft deleted conversationMessages", async () => {
      const conversationMessageData = createConversationMessage(3)
      const createResult = await implHandleCreateConversationMessage(
        conversationMessageData,
        businessLogic,
      )
      expect(createResult.status).toBe(201)

      const deleteResult = await implHandleDeleteConversationMessage(
        createResult.body.data.id,
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)

      const conversationMessageUpdate = createConversationMessageUpdate(1)

      const result = await implHandleUpdateConversationMessage(
        createResult.body.data.id,
        conversationMessageUpdate,
        businessLogic,
      )

      expect(result.status).toBe(404)
      expect(result.body.status).toBe("failed")
    })

    it("should not include soft deleted conversationMessages in search", async () => {
      const conversationMessageData1 = createConversationMessage(3) // Conversationest ConversationMessage"
      const createResult = await implHandleCreateConversationMessage(
        conversationMessageData1,
        businessLogic,
      )
      expect(createResult.status).toBe(201)

      // Soft delete the conversationMessage
      const deleteResult = await implHandleDeleteConversationMessage(
        createResult.body.data.id,
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)

      // Search should not find the soft deleted conversationMessage
      const searchResult = await implHandleGetAllConversationMessages(
        businessLogic,
        {
          search: "Test",
        },
      )
      expect(searchResult.status).toBe(200)
      expect(searchResult.body.data).toHaveLength(0)
    })
  })

  describe("Restore", () => {
    it("should restore a soft deleted conversationMessages", async () => {
      const conversationMessageData = createConversationMessageWithDescription()
      const createResult = await implHandleCreateConversationMessage(
        conversationMessageData,
        businessLogic,
      )
      expect(createResult.status).toBe(201)

      // Soft delete the conversationMessages
      const deleteResult = await implHandleDeleteConversationMessage(
        createResult.body.data.id,
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)

      const getResult = await implHandleGetConversationMessage(
        createResult.body.data.id,
        businessLogic,
      )
      expect(getResult.status).toBe(404)

      // Restore the conversationMessages
      const restoreResult = await implHandleRestoreConversationMessage(
        createResult.body.data.id,
        businessLogic,
      )

      expect(restoreResult.status).toBe(200)
      expect(restoreResult.body.status).toBe("success")

      // ConversationMessage should be accessible again
      const restoredResult = await implHandleGetConversationMessage(
        createResult.body.data.id,
        businessLogic,
      )
      expect(restoredResult.status).toBe(200)
      expect(restoredResult.body.data?.deletedAt).toBeUndefined()

      // Count should include the restored conversationMessages
      expect(await dbRepository.getConversationMessageCount()).toBe(1)
    })

    it("should fail to restore a non-existent conversationMessages", async () => {
      const restoreResult = await implHandleRestoreConversationMessage(
        "507f1f77bcf86cd799439011",
        businessLogic,
      )
      expect(restoreResult.status).toBe(404)
      expect(restoreResult.body.status).toBe("failed")
    })

    it("should fail to restore a conversationMessages that was never deleted", async () => {
      const conversationMessageData = createConversationMessage(3)
      const createResult = await implHandleCreateConversationMessage(
        conversationMessageData,
        businessLogic,
      )
      expect(createResult.status).toBe(201)

      const restoreResult = await implHandleRestoreConversationMessage(
        createResult.body.data.id,
        businessLogic,
      )
      expect(restoreResult.status).toBe(404)
      expect(restoreResult.body.status).toBe("failed")
    })

    it("should fail to restore a hard deleted conversationMessages", async () => {
      const conversationMessageData = createConversationMessage(3)
      const createResult = await implHandleCreateConversationMessage(
        conversationMessageData,
        businessLogic,
      )
      expect(createResult.status).toBe(201)

      // Hard delete the conversationMessages
      const deleteResult = await implHandleDeleteConversationMessage(
        createResult.body.data.id,
        businessLogic,
        true,
      )
      expect(deleteResult.status).toBe(200)

      const restoreResult = await implHandleRestoreConversationMessage(
        createResult.body.data.id,
        businessLogic,
      )
      expect(restoreResult.status).toBe(404)
      expect(restoreResult.body.status).toBe("failed")
    })

    it("should fail with empty conversationMessages ID", async () => {
      const restoreResult = await implHandleRestoreConversationMessage(
        "",
        businessLogic,
      )
      expect(restoreResult.status).toBe(400)
      expect(restoreResult.body.status).toBe("failed")
      expect(restoreResult.body.error).toContain(
        "ConversationMessage ID is required",
      )
    })

    it("should fail with whitespace-only conversationMessages ID", async () => {
      const restoreResult = await implHandleRestoreConversationMessage(
        "   ",
        businessLogic,
      )
      expect(restoreResult.status).toBe(400)
      expect(restoreResult.body.status).toBe("failed")
      expect(restoreResult.body.error).toContain(
        "ConversationMessage ID is required",
      )
    })

    it("should update updatedAt when restoring", async () => {
      const conversationMessageData = createConversationMessage(3)
      const createResult = await implHandleCreateConversationMessage(
        conversationMessageData,
        businessLogic,
      )
      expect(createResult.status).toBe(201)

      const originalUpdatedAt = createResult.body.data.updatedAt

      // Wait a bit to ensure different timestamp
      await new Promise((resolve) => setTimeout(resolve, 10))

      // Soft delete and restore
      const deleteResult = await implHandleDeleteConversationMessage(
        createResult.body.data.id,
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)

      const restoreResult = await implHandleRestoreConversationMessage(
        createResult.body.data.id,
        businessLogic,
      )
      expect(restoreResult.status).toBe(200)

      const getResult = await implHandleGetConversationMessage(
        createResult.body.data.id,
        businessLogic,
      )
      expect(getResult.status).toBe(200)
      expect(getResult.body.data?.updatedAt.getTime()).toBeGreaterThan(
        originalUpdatedAt.getTime(),
      )
    })
  })

  describe("Duplicate Name Validation with Soft Delete", () => {
    it("should allow creating conversationMessages with STRING_FIELD of soft deleted conversationMessages", async () => {
      // Create and soft delete a conversationMessages
      const conversationMessageData1 = crConversationTestConversationMessage()
      const createResult1 = await implHandleCreateConversationMessage(
        conversationMessageData1,
        businessLogic,
      )
      expect(createResult1.status).toBe(201)

      const deleteResult = await implHandleDeleteConversationMessage(
        createResult1.body.data.id,
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)

      // Should be able to create new conversationMessages with same STRING_FIELD
      const conversationMessageData2 = crConversationTestConversationMessage2()
      const createResult2 = await implHandleCreateConversationMessage(
        conversationMessageData2,
        businessLogic,
      )

      expect(createResult2.status).toBe(201)
      expect(createResult2.body.data.STRING_FIELD).toBe(
        conversationMessageData2.STRING_FIELD,
      )
      expect(await dbRepository.getConversationMessageCount()).toBe(1)
    })

    it("should prevent creating conversationMessages with STRING_FIELD of active conversationMessages", async () => {
      const conversationMessageData1 = crConversationTestConversationMessage()
      const createResult1 = await implHandleCreateConversationMessage(
        conversationMessageData1,
        businessLogic,
      )
      expect(createResult1.status).toBe(201)

      const conversationMessageData2 = crConversationTestConversationMessage2()
      const createResult2 = await implHandleCreateConversationMessage(
        conversationMessageData2,
        businessLogic,
      )

      expect(createResult2.status).toBe(409)
      expect(createResult2.body.status).toBe("failed")
      expect(createResult2.body.error).toContain(
        "ConversationMessage with the same STRING_FIELD already exists",
      )
    })

    it("should prevent restoring conversationMessages if STRING_FIELD is now taken", async () => {
      // Create and soft delete a conversationMessages
      const conversationMessageData1 = crConversationTestConversationMessage()
      const createResult1 = await implHandleCreateConversationMessage(
        conversationMessageData1,
        businessLogic,
      )
      expect(createResult1.status).toBe(201)

      const deleteResult = await implHandleDeleteConversationMessage(
        createResult1.body.data.id,
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)

      // Create new conversationMessages with same STRING_FIELD
      const conversationMessageData2 = crConversationTestConversationMessage2()
      const createResult2 = await implHandleCreateConversationMessage(
        conversationMessageData2,
        businessLogic,
      )
      expect(createResult2.status).toBe(201)

      // Should not be able to restore the first conversationMessages
      const restoreResult = await implHandleRestoreConversationMessage(
        createResult1.body.data.id,
        businessLogic,
      )
      expect(restoreResult.status).toBe(404)
      expect(restoreResult.body.status).toBe("failed")
    })
  })
})
