import { z } from "zod"

export const DatasourceCreateSchema = z
  .object({
    name: z
      .string()
      .min(1, "Name is required")
      .max(100, "Name must be less than 100 characters"),
    type: z.string().min(1, "Type is required"),
    url: z.string().url("Must be a valid URL").optional(),
    content: z.string().optional(),
    accessKey: z.string().optional(),
    isActive: z.boolean().optional().default(true),
  })
  .refine(
    (data) => {
      // For TEXT type, content is required and url is not needed
      if (data.type === "TEXT") {
        return data.content && data.content.trim().length > 0
      }
      // For other types, url is required
      return data.url && data.url.trim().length > 0
    },
    {
      message: "TEXT type requires content, other types require URL",
      path: ["content", "url"],
    },
  )

export const DatasourceUpdateSchema = z.object({
  name: z
    .string()
    .min(1, "Name is required")
    .max(100, "Name must be less than 100 characters")
    .optional(),
  type: z.string().min(1, "Type is required").optional(),
  url: z.string().url("Must be a valid URL").optional(),
  content: z.string().optional(),
  accessKey: z.string().optional(),
  isActive: z.boolean().optional(),
})

export type DatasourceCreateSchemaType = z.infer<typeof DatasourceCreateSchema>
export type DatasourceUpdateSchemaType = z.infer<typeof DatasourceUpdateSchema>
