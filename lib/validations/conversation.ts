import { z } from "zod"

export const ConversationCreateSchema = z.object({
  name: z.string().min(1, "Name is required"),
  description: z.string().optional(),
  participants: z
    .array(z.string().min(1, "Participant ID cannot be empty"))
    .min(1, "At least one participant is required"),
  tags: z.array(z.string()).optional(),
  isActive: z.boolean().optional().default(true),
  isAiEnabled: z.boolean().optional().default(true),
  // Note: createdBy and organizationId will be added from SessionContext
})

export const ConversationUpdateSchema = z.object({
  name: z.string().min(1, "Name is required").optional(),
  description: z.string().optional(),
  tags: z.array(z.string()).optional(),
  isActive: z.boolean().optional(),
  isAiEnabled: z.boolean().optional(),
  isManualSendAiAnswer: z.boolean().optional(),
})

export const ConversationIdSchema = z.object({
  id: z.string().min(1, "Conversation ID is required"),
})

export type ConversationCreateInput = z.infer<typeof ConversationCreateSchema>
export type ConversationUpdateInput = z.infer<typeof ConversationUpdateSchema>
export type ConversationIdInput = z.infer<typeof ConversationIdSchema>
