import {
  TestConversation,
  TestMessage,
} from "@/lib/repositories/testConversations"
import { BaseAPI } from "./baseApi"
import { PaginatedResponse, PagingAndSearch } from "./types"
import { TestWebhookPayload } from "@/components/ai-test-chat"

export type TestConversationPayload = Partial<TestConversation>

export interface TestConversationCreatePayload {
  customerName: string
  customerPhone: string
  scenario: string
  scenarioDescription: string
  sampleMessages: string[]
  title: string
}

export interface TestMessagePayload {
  id?: string
  content: string
  senderId: string
  senderName: string
  isFromCustomer?: boolean
  isFromAI?: boolean
  timestamp?: string
  messageType?: "TEXT" | "IMAGE" | "LOG"
  metadata?: any
}

export interface ClearMessagesResponse {
  cleared: boolean
  messagesDeleted: number
}

export interface AddMessageResponse {
  message: TestMessage
  conversation: TestConversation
}

export class TestConversationsAPI extends BaseAPI {
  static All(params: PagingAndSearch<{}>) {
    const queryString = `?${new URLSearchParams(
      PagingAndSearch.toRecordString(params),
    ).toString()}`
    return new BaseAPI(`/test-conversations${queryString}`).build<
      PaginatedResponse<TestConversation>
    >()
  }

  static Detail = (conversationId: string) =>
    new BaseAPI(
      `/test-conversations/${conversationId}`,
    ).build<TestConversation>()

  static Create = (body: TestConversationCreatePayload) =>
    new BaseAPI(`/test-conversations`, body, "POST").build<TestConversation>()

  static Update = (conversationId: string, body: TestConversationPayload) =>
    new BaseAPI(
      `/test-conversations/${conversationId}`,
      body,
      "PUT",
    ).build<TestConversation>()

  static Delete = (conversationId: string) =>
    new BaseAPI(
      `/test-conversations/${conversationId}`,
      undefined,
      "DELETE",
    ).build<{
      deleted: boolean
    }>()

  static Clear = (conversationId: string) =>
    new BaseAPI(
      `/test-conversations/${conversationId}/clear`,
      {},
      "POST",
    ).build<ClearMessagesResponse>()

  // Messages endpoints
  static GetMessages = (conversationId: string) =>
    new BaseAPI(`/test-conversations/${conversationId}/messages`).build<
      TestMessage[]
    >()

  static AddMessage = (conversationId: string, body: TestMessagePayload) =>
    new BaseAPI(
      `/test-conversations/${conversationId}/messages`,
      body,
      "POST",
    ).build<AddMessageResponse>()

  static SendMessage = (conversationId: string, payload: TestWebhookPayload) =>
    new BaseAPI(
      `/test-conversations/${conversationId}/webhook/test`,
      payload,
      "POST",
    ).build<{
      conversationId: string
      messageId: string
    }>()
}
