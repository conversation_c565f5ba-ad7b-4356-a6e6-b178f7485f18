import { BaseAPI } from "./baseApi"
import {
  Broadcast,
  BroadcastRecipient,
} from "@/lib/repositories/broadcast/interface"
import { PagingAndSearch, PaginatedResponse } from "./types"

export interface BroadcastPayload {
  title: string
  message: string
  manualSelectedTargetRecipients?: string[] // Made optional for tag-based selection
  scheduledAt?: string | Date
  deviceId: string

  // New fields for tag-based broadcasting
  recipientTags?: string[]
  excludedRecipientIds?: string[]
}

export class BroadcastAPI extends BaseAPI {
  static All(params: PagingAndSearch<{}>) {
    const queryString = `?${new URLSearchParams(
      PagingAndSearch.toRecordString(params),
    ).toString()}`
    return new BaseAPI(`/broadcast${queryString}`).build<
      PaginatedResponse<Broadcast>
    >()
  }

  static Detail = (broadcastId: string) =>
    new BaseAPI(`/broadcast/${broadcastId}`).build<Broadcast>()

  static Create = (body: BroadcastPayload) =>
    new BaseAPI(`/broadcast`, body, "POST").build<Broadcast>()

  static Update = (broadcastId: string, body: BroadcastPayload) =>
    new BaseAPI(`/broadcast/${broadcastId}`, body, "PUT").build<Broadcast>()

  static Delete = (broadcastId: string) =>
    new BaseAPI(`/broadcast/${broadcastId}`, undefined, "DELETE").build<{
      success: boolean
    }>()

  static Duplicate = (broadcastId: string, newTitle?: string) =>
    new BaseAPI(
      `/broadcast/${broadcastId}/duplicate`,
      { newTitle },
      "POST",
    ).build<Broadcast>()

  // Broadcast operations
  static Start = (broadcastId: string) =>
    new BaseAPI(`/broadcast/${broadcastId}/start`, {}, "POST").build<{
      success: boolean
      message: string
    }>()

  static Cancel = (broadcastId: string) =>
    new BaseAPI(`/broadcast/${broadcastId}/cancel`, {}, "POST").build<{
      success: boolean
      message: string
    }>()

  static Progress = (broadcastId: string) =>
    new BaseAPI(`/broadcast/${broadcastId}/progress`).build<{
      totalTargets: number
      sentCount: number
      failedCount: number
      pendingCount: number
      successRate: number
      status: string
    }>()

  static Recipients = (broadcastId: string, params: PagingAndSearch<{}>) => {
    const queryString = `?${new URLSearchParams(
      PagingAndSearch.toRecordString(params),
    ).toString()}`
    return new BaseAPI(
      `/broadcast/${broadcastId}/recipients${queryString}`,
    ).build<PaginatedResponse<BroadcastRecipient>>()
  }
}
