//@ts-ignore
import { beforeEach, describe, expect, it, test } from "bun:test"
import { implHandleForgotPassword } from "@/app/api/v1/auth/forgot-password/impl"
import { MongoAuthDBRepository } from "@/lib/repositories/auth"
import { AuthBusinessLogicInterface } from "@/lib/repositories/auth/AuthBusinessLogicInterface"
import { AuthBusinessLogic } from "@/lib/repositories/auth/BusinessLogic"
import type { UserRegister } from "@/lib/types/base"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"
import { TestAuthDBRepositoryWrapper } from "./TestDBRepositoryWrapper"

describe("Forgot Password API Tests", () => {
  let businessLogic: AuthBusinessLogicInterface
  let dbRepository: TestAuthDBRepositoryWrapper

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("Auth")
    await driver.connect()
    const originalDb = new MongoAuthDBRepository(driver)
    dbRepository = new TestAuthDBRepositoryWrapper(originalDb, driver)
    businessLogic = new AuthBusinessLogic(dbRepository)
    await dbRepository.clear()
  })

  describe("POST /api/v1/auth/forgot-password", () => {
    it("should successfully initiate password reset for existing user", async () => {
      // Setup: Register a user
      const userData: UserRegister = {
        email: "<EMAIL>",
        password: "password123",
        name: "Forgot User",
      }

      await businessLogic.register(userData)

      // Test: Request password reset
      const forgotPasswordData = {
        email: "<EMAIL>",
      }

      const result = await implHandleForgotPassword(
        forgotPasswordData,
        businessLogic,
      )

      // Verify API response
      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body?.data.message).toContain(
        "If the email exists, a reset link has been sent",
      )
      expect(result.body?.data.resetToken).toBeDefined()

      // Verify reset token was created
      expect(await dbRepository.getPasswordResetCount()).toBe(1)
    })

    it("should handle non-existent email gracefully", async () => {
      const forgotPasswordData = {
        email: "<EMAIL>",
      }

      const result = await implHandleForgotPassword(
        forgotPasswordData,
        businessLogic,
      )

      // Should still return success for security reasons (don't reveal if email exists)
      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body?.data.message).toContain(
        "If the email exists, a reset link has been sent",
      )

      // But no reset token should be created
      expect(await dbRepository.getPasswordResetCount()).toBe(0)
    })

    it("should fail with missing email", async () => {
      const forgotPasswordData = {}

      const result = await implHandleForgotPassword(
        forgotPasswordData,
        businessLogic,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(Array.isArray(result.body.error)).toBe(true)
    })

    it("should fail with empty email", async () => {
      const forgotPasswordData = {
        email: "",
      }

      const result = await implHandleForgotPassword(
        forgotPasswordData,
        businessLogic,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(Array.isArray(result.body.error)).toBe(true)
    })

    it("should fail with invalid email format", async () => {
      const forgotPasswordData = {
        email: "invalid-email",
      }

      const result = await implHandleForgotPassword(
        forgotPasswordData,
        businessLogic,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(Array.isArray(result.body.error)).toBe(true)
    })

    it("should handle case-sensitive email lookup", async () => {
      // Setup: Register a user with mixed case email
      const userData: UserRegister = {
        email: "<EMAIL>",
        password: "password123",
        name: "Case User",
      }

      await businessLogic.register(userData)

      // Test: Request reset with lowercase email
      const forgotPasswordData = {
        email: "<EMAIL>",
      }

      const result = await implHandleForgotPassword(
        forgotPasswordData,
        businessLogic,
      )

      // Current implementation treats emails as case-sensitive
      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(await dbRepository.getPasswordResetCount()).toBe(0) // No reset token created
    })

    it("should replace existing reset token for same user", async () => {
      // Setup: Register a user
      const userData: UserRegister = {
        email: "<EMAIL>",
        password: "password123",
        name: "Replace User",
      }

      await businessLogic.register(userData)

      // First reset request
      const forgotPasswordData = {
        email: "<EMAIL>",
      }

      const result1 = await implHandleForgotPassword(
        forgotPasswordData,
        businessLogic,
      )
      expect(result1.status).toBe(200)
      expect(await dbRepository.getPasswordResetCount()).toBe(1)

      const firstToken = result1.body.data.resetToken

      // Second reset request
      const result2 = await implHandleForgotPassword(
        forgotPasswordData,
        businessLogic,
      )
      expect(result2.status).toBe(200)
      expect(await dbRepository.getPasswordResetCount()).toBe(2) // Current implementation creates new tokens

      const secondToken = result2.body.data.resetToken

      // Tokens should be different
      expect(firstToken).not.toBe(secondToken)
    })

    it("should handle multiple users requesting resets", async () => {
      // Setup: Register multiple users
      const user1: UserRegister = {
        email: "<EMAIL>",
        password: "password123",
        name: "User 1",
      }

      const user2: UserRegister = {
        email: "<EMAIL>",
        password: "password123",
        name: "User 2",
      }

      await businessLogic.register(user1)
      await businessLogic.register(user2)

      // Both request password reset
      const result1 = await implHandleForgotPassword(
        { email: "<EMAIL>" },
        businessLogic,
      )
      const result2 = await implHandleForgotPassword(
        { email: "<EMAIL>" },
        businessLogic,
      )

      expect(result1.status).toBe(200)
      expect(result2.status).toBe(200)
      expect(await dbRepository.getPasswordResetCount()).toBe(2)

      // Tokens should be different
      expect(result1.body.data.resetToken).not.toBe(
        result2.body.data.resetToken,
      )
    })

    it("should handle whitespace in email", async () => {
      // Setup: Register a user
      const userData: UserRegister = {
        email: "<EMAIL>",
        password: "password123",
        name: "Trim User",
      }

      await businessLogic.register(userData)

      // Test: Request reset with whitespace
      const forgotPasswordData = {
        email: "  <EMAIL>  ",
      }

      const result = await implHandleForgotPassword(
        forgotPasswordData,
        businessLogic,
      )

      // Current implementation doesn't trim whitespace
      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(Array.isArray(result.body.error)).toBe(true)
    })

    it("should generate unique reset tokens", async () => {
      // Setup: Register a user
      const userData: UserRegister = {
        email: "<EMAIL>",
        password: "password123",
        name: "Unique User",
      }

      await businessLogic.register(userData)

      // Request multiple resets and collect tokens
      const tokens = new Set()

      for (let i = 0; i < 5; i++) {
        const result = await implHandleForgotPassword(
          { email: "<EMAIL>" },
          businessLogic,
        )
        tokens.add(result.body?.data.resetToken)
      }

      // All tokens should be unique (though only the last one is valid)
      expect(tokens.size).toBe(5)
    })
  })
})
