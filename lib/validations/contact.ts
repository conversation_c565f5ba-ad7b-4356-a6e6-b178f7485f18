import { z } from "zod"

// Contact validation schemas
export const ContactCreateSchema = z.object({
  name: z
    .string()
    .min(1, "Name is required")
    .min(2, "Name must be at least 2 characters")
    .max(100, "Name must be less than 100 characters")
    .trim(),

  phone: z
    .string()
    .min(1, "Phone number is required")
    .min(10, "Phone number must be at least 10 digits")
    .max(20, "Phone number must be less than 20 characters")
    .regex(/^[\d+\-\s()]+$/, "Phone number contains invalid characters"),

  email: z
    .string()
    .email("Invalid email format")
    .max(255, "Email must be less than 255 characters")
    .optional()
    .or(z.literal("")),

  tags: z
    .array(z.string().trim().min(1))
    .max(10, "Maximum 10 tags allowed")
    .optional(),

  notes: z
    .array(
      z.object({
        text: z.string().trim().min(1, "Note text cannot be empty"),
      }),
    )
    .max(20, "Maximum 20 notes allowed")
    .optional(),
})

export const ContactUpdateSchema = z.object({
  name: z
    .string()
    .min(2, "Name must be at least 2 characters")
    .max(100, "Name must be less than 100 characters")
    .trim()
    .optional(),

  phone: z
    .string()
    .min(10, "Phone number must be at least 10 digits")
    .max(20, "Phone number must be less than 20 characters")
    .regex(/^[\d+\-\s()]+$/, "Phone number contains invalid characters")
    .optional(),

  email: z
    .string()
    .email("Invalid email format")
    .max(255, "Email must be less than 255 characters")
    .optional()
    .or(z.literal("")),

  tags: z
    .array(z.string().trim().min(1))
    .max(10, "Maximum 10 tags allowed")
    .optional(),

  notes: z
    .array(
      z.object({
        text: z.string().trim().min(1, "Note text cannot be empty"),
        createdAt: z.string().datetime("Invalid date format"),
      }),
    )
    .max(20, "Maximum 20 notes allowed")
    .optional(),
})

export const ContactSearchSchema = z.object({
  keyword: z
    .string()
    .min(1, "Search keyword is required")
    .max(100, "Search keyword must be less than 100 characters")
    .trim(),
})

export const ContactPaginationSchema = z.object({
  limit: z
    .number()
    .int("Limit must be an integer")
    .min(1, "Limit must be at least 1")
    .max(100, "Limit must be at most 100")
    .optional(),

  offset: z
    .number()
    .int("Offset must be an integer")
    .min(0, "Offset must be non-negative")
    .optional(),

  sortBy: z.enum(["name", "createdAt", "updatedAt"]).optional(),

  sortOrder: z.enum(["ASC", "DESC"]).optional(),

  filters: z
    .object({
      tags: z.array(z.string().trim().min(1)).optional(),

      hasEmail: z.boolean().optional(),

      createdAfter: z
        .string()
        .datetime("Invalid date format")
        .transform((str) => new Date(str))
        .optional(),

      createdBefore: z
        .string()
        .datetime("Invalid date format")
        .transform((str) => new Date(str))
        .optional(),
    })
    .optional(),
})

// CSV-compatible schemas (for bulk import/export)
// These schemas accept string formats that are common in CSV files
export const ContactCreateCSVSchema = z.object({
  name: z
    .string()
    .min(1, "Name is required")
    .min(2, "Name must be at least 2 characters")
    .max(100, "Name must be less than 100 characters")
    .trim(),

  phone: z
    .string()
    .min(1, "Phone number is required")
    .min(10, "Phone number must be at least 10 digits")
    .max(20, "Phone number must be less than 20 characters")
    .regex(/^[\d+\-\s()]+$/, "Phone number contains invalid characters"),

  email: z
    .string()
    .email("Invalid email format")
    .max(255, "Email must be less than 255 characters")
    .optional()
    .or(z.literal("")),

  // Accept comma-separated string for tags (CSV format)
  tags: z
    .string()
    .optional()
    .transform((val) => {
      if (!val || val.trim() === "") return undefined
      return val.split(",").map(tag => tag.trim()).filter(tag => tag.length > 0)
    })
    .refine((tags) => !tags || tags.length <= 10, "Maximum 10 tags allowed"),

  // Accept string for notes (CSV format) - will be converted to array
  notes: z
    .string()
    .optional()
    .transform((val) => {
      if (!val || val.trim() === "") return undefined
      return [{
        text: val.trim(),
        createdAt: new Date().toISOString(),
      }]
    }),
})

export const ContactUpdateCSVSchema = z.object({
  name: z
    .string()
    .min(2, "Name must be at least 2 characters")
    .max(100, "Name must be less than 100 characters")
    .trim()
    .optional(),

  phone: z
    .string()
    .min(10, "Phone number must be at least 10 digits")
    .max(20, "Phone number must be less than 20 characters")
    .regex(/^[\d+\-\s()]+$/, "Phone number contains invalid characters")
    .optional(),

  email: z
    .string()
    .email("Invalid email format")
    .max(255, "Email must be less than 255 characters")
    .optional()
    .or(z.literal("")),

  // Accept comma-separated string for tags (CSV format)
  tags: z
    .string()
    .optional()
    .transform((val) => {
      if (!val || val.trim() === "") return undefined
      return val.split(",").map(tag => tag.trim()).filter(tag => tag.length > 0)
    })
    .refine((tags) => !tags || tags.length <= 10, "Maximum 10 tags allowed"),

  // Accept string for notes (CSV format)
  notes: z
    .string()
    .optional()
    .transform((val) => {
      if (!val || val.trim() === "") return undefined
      return [{
        text: val.trim(),
        createdAt: new Date().toISOString(),
      }]
    }),
})

// Type exports
export type ContactCreateInput = z.infer<typeof ContactCreateSchema>
export type ContactUpdateInput = z.infer<typeof ContactUpdateSchema>
export type ContactSearchInput = z.infer<typeof ContactSearchSchema>
export type ContactPaginationInput = z.infer<typeof ContactPaginationSchema>

// CSV-compatible type exports (before transformation)
export type ContactCreateCSVInput = z.input<typeof ContactCreateCSVSchema>
export type ContactUpdateCSVInput = z.input<typeof ContactUpdateCSVSchema>

// CSV-compatible type exports (after transformation)
export type ContactCreateCSVOutput = z.output<typeof ContactCreateCSVSchema>
export type ContactUpdateCSVOutput = z.output<typeof ContactUpdateCSVSchema>
