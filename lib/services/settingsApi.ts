// src/api/globalApi.ts

import { BaseAPI } from "./baseApi"

interface GlobalAISetting {
  enabled: boolean
}

export class SettingsAPI extends BaseAPI {
  static GetGlobalAISetting = () =>
    new BaseAPI(`/settings/global-ai`).build<GlobalAISetting>()

  static UpdateGlobalAISetting = (enabled: boolean) =>
    new BaseAPI(
      `/settings/global-ai`,
      { enabled },
      "PUT",
    ).build<GlobalAISetting>()
}
