//@ts-ignore
import { beforeEach, describe, expect, it, test } from "bun:test"
import { implHandleChangePassword } from "@/app/api/v1/auth/change-password/impl"
import { MongoAuthDBRepository } from "@/lib/repositories/auth"
import { AuthBusinessLogicInterface } from "@/lib/repositories/auth/AuthBusinessLogicInterface"
import { AuthBusinessLogic } from "@/lib/repositories/auth/BusinessLogic"
import type { UserRegister } from "@/lib/types/base"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"
import { TestAuthDBRepositoryWrapper } from "./TestDBRepositoryWrapper"

describe("Change Password API Tests", () => {
  let businessLogic: AuthBusinessLogicInterface
  let dbRepository: TestAuthDBRepositoryWrapper

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("Auth")
    await driver.connect()
    const originalDb = new MongoAuthDBRepository(driver)
    dbRepository = new TestAuthDBRepositoryWrapper(originalDb, driver)
    businessLogic = new AuthBusinessLogic(dbRepository)
    await dbRepository.clear()
  })

  describe("POST /api/v1/auth/change-password", () => {
    it("should successfully change password with valid data", async () => {
      // Setup: Register a user
      const userData: UserRegister = {
        email: "<EMAIL>",
        password: "oldpassword123",
        name: "Change Pass User",
      }

      const registerResult = await businessLogic.register(userData)
      const userId = registerResult.user.id

      // Test: Change password
      const changePasswordData = {
        currentPassword: "oldpassword123",
        newPassword: "newpassword456",
        confirmPassword: "newpassword456",
      }

      const result = await implHandleChangePassword(
        changePasswordData,
        businessLogic,
        userId,
      )

      // Verify API response
      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body?.data.message).toContain(
        "Password changed successfully",
      )

      // Verify old password no longer works
      await expect(
        businessLogic.login({
          email: "<EMAIL>",
          password: "oldpassword123",
        }),
      ).rejects.toThrow()

      // Verify new password works
      const newLoginResult = await businessLogic.login({
        email: "<EMAIL>",
        password: "newpassword456",
      })
      expect(newLoginResult.user.id).toBe(userId)
    })

    it("should fail change password with wrong current password", async () => {
      // Setup: Register a user
      const userData: UserRegister = {
        email: "<EMAIL>",
        password: "correctpassword",
        name: "Wrong Pass User",
      }

      const registerResult = await businessLogic.register(userData)

      // Test: Try to change password with wrong current password
      const changePasswordData = {
        currentPassword: "wrongpassword",
        newPassword: "newpassword456",
        confirmPassword: "newpassword456",
      }

      const result = await implHandleChangePassword(
        changePasswordData,
        businessLogic,
        registerResult.user.id,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("Current password is incorrect")
    })

    it("should fail change password with missing confirmPassword", async () => {
      const changePasswordData = {
        currentPassword: "oldpassword123",
        newPassword: "newpassword456",
        // Missing confirmPassword
      }

      const result = await implHandleChangePassword(
        changePasswordData,
        businessLogic,
        "user-123",
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(Array.isArray(result.body.error)).toBe(true)
    })

    it("should fail change password with missing current password", async () => {
      const changePasswordData = {
        newPassword: "newpassword456",
        confirmPassword: "newpassword456",
      }

      const result = await implHandleChangePassword(
        changePasswordData,
        businessLogic,
        "user-123",
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(Array.isArray(result.body.error)).toBe(true)
    })

    it("should fail change password with missing new password", async () => {
      const changePasswordData = {
        currentPassword: "oldpassword123",
        confirmPassword: "newpassword456",
      }

      const result = await implHandleChangePassword(
        changePasswordData,
        businessLogic,
        "user-123",
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(Array.isArray(result.body.error)).toBe(true)
    })

    it("should fail change password with weak new password", async () => {
      // Setup: Register a user
      const userData: UserRegister = {
        email: "<EMAIL>",
        password: "strongpassword123",
        name: "Weak Pass User",
      }

      const registerResult = await businessLogic.register(userData)

      // Test: Try to change to weak password
      const changePasswordData = {
        currentPassword: "strongpassword123",
        newPassword: "123", // Too short
        confirmPassword: "123",
      }

      const result = await implHandleChangePassword(
        changePasswordData,
        businessLogic,
        registerResult.user.id,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(Array.isArray(result.body.error)).toBe(true)
    })

    it("should fail change password with non-existent user", async () => {
      const changePasswordData = {
        currentPassword: "oldpassword123",
        newPassword: "newpassword456",
        confirmPassword: "newpassword456",
      }

      const result = await implHandleChangePassword(
        changePasswordData,
        businessLogic,
        "non-existent-user-id",
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(Array.isArray(result.body.error)).toBe(true)
    })

    it("should handle change password with same current and new password", async () => {
      // Setup: Register a user
      const userData: UserRegister = {
        email: "<EMAIL>",
        password: "samepassword123",
        name: "Same Pass User",
      }

      const registerResult = await businessLogic.register(userData)

      // Test: Try to change to same password (current implementation allows this)
      const changePasswordData = {
        currentPassword: "samepassword123",
        newPassword: "samepassword123",
        confirmPassword: "samepassword123",
      }

      const result = await implHandleChangePassword(
        changePasswordData,
        businessLogic,
        registerResult.user.id,
      )

      // Current implementation doesn't validate same password
      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body?.data.message).toContain(
        "Password changed successfully",
      )
    })

    it("should fail change password with empty passwords", async () => {
      const changePasswordData = {
        currentPassword: "",
        newPassword: "",
        confirmPassword: "",
      }

      const result = await implHandleChangePassword(
        changePasswordData,
        businessLogic,
        "user-123",
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(Array.isArray(result.body.error)).toBe(true)
    })

    it("should handle token validation after password change", async () => {
      // Setup: Register a user and get token
      const userData: UserRegister = {
        email: "<EMAIL>",
        password: "oldpassword123",
        name: "Invalidate User",
      }

      const registerResult = await businessLogic.register(userData)
      const oldToken = registerResult.token

      // Verify old token works
      const validatedUser1 = await businessLogic.validateToken(oldToken)
      expect(validatedUser1).toBeTruthy()

      // Change password
      const changePasswordData = {
        currentPassword: "oldpassword123",
        newPassword: "newpassword456",
        confirmPassword: "newpassword456",
      }

      await implHandleChangePassword(
        changePasswordData,
        businessLogic,
        registerResult.user.id,
      )

      // Current implementation doesn't invalidate tokens on password change
      const validatedUser2 = await businessLogic.validateToken(oldToken)
      expect(validatedUser2).toBeTruthy() // Token still works
    })
  })
})
