//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test"
import { ConversationBusinessLogicInterface } from "@/lib/repositories/conversations/interface"
import { ConversationBusinessLogic } from "@/lib/repositories/conversations/BusinessLogic"
import { MongoConversationRepository } from "@/lib/repositories/conversations/MongoRepository"
import { TestConversationDBRepositoryWrapper } from "./TestDBRepositoryWrapper"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"
import {
  implHandleCreateConversation,
  implHandleGetAllConversations,
  implHandleDeleteConversation,
} from "@/app/api/v1/conversations/impl"
import {
  createMultipleConversations,
  createSearchByNameParams,
  createSearchByDescriptionParams,
  createEmptySearchParams,
  createWhitespaceSearchParams,
  createNonExistentSearchParams,
  createVipTagFilterParams,
  createCustomerTagFilterParams,
  createPaginationParams,
  createSortByNameAscParams,
  createSearchAndTagParams,
  createIncludeDeletedParams,
  createEmptyTagParams,
  createWhitespaceTagParams,
  createNonExistentTagParams,
} from "./object_creator"

describe("Consolidated Conversation API Tests", () => {
  let businessLogic: ConversationBusinessLogicInterface
  let dbRepository: TestConversationDBRepositoryWrapper

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("Conversation")
    await driver.connect()
    const originalDb = new MongoConversationRepository(driver)
    dbRepository = new TestConversationDBRepositoryWrapper(originalDb, driver)
    businessLogic = new ConversationBusinessLogic(dbRepository)
    await dbRepository.clear()
  })

  const testConversations = createMultipleConversations()

  describe("implHandleGetAllConversations - Consolidated Function", () => {
    beforeEach(async () => {
      for (const conversationsData of createConversationsForBulkUpdate()) {
        await implHandleCreateConversation(conversationsData, businessLogic)
      }
    })

    it("should get all conversations when no parameters provided", async () => {
      const result = await implHandleGetAllConversations(businessLogic)

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(4)
      expect(result.body.data?.total).toBe(4)
    })

    it("should search conversations by STRING_FIELD", async () => {
      const params = createSearchByNameParams()
      const result = await implHandleGetAllConversations(businessLogic, params)

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(2) // John Doe Conversation and Bob Johnson Conversation

      const STRING_FIELDs = result.body.data?.items.map(
        (c: any) => c.STRING_FIELD,
      )
      expect(STRING_FIELDs).toContain(testConversations[0].STRING_FIELD) // John Doe Conversation
      expect(STRING_FIELDs).toContain(testConversations[2].STRING_FIELD) // Bob Johnson Conversation
    })

    it("should search conversations by ARRAY_FIELD2", async () => {
      const params = createSearchByDescriptionParams()
      const result = await implHandleGetAllConversations(businessLogic, params)

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(4) // All conversations have "processing" in ARRAY_FIELD2
    })

    it("should filter conversations by tag", async () => {
      const params = createVipTagFilterParams()
      const result = await implHandleGetAllConversations(businessLogic, params)

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(2) // John Doe Conversation and Bob Johnson Conversation

      const STRING_FIELDs = result.body.data?.items.map(
        (c: any) => c.STRING_FIELD,
      )
      expect(STRING_FIELDs).toContain(testConversations[0].STRING_FIELD) // John Doe Conversation
      expect(STRING_FIELDs).toContain(testConversations[2].STRING_FIELD) // Bob Johnson Conversation
    })

    it("should filter conversations by Customer tag", async () => {
      const params = createCustomerTagFilterParams()
      const result = await implHandleGetAllConversations(businessLogic, params)

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(2) // John Doe Conversation and Jane Smith Conversation

      const STRING_FIELDs = result.body.data?.items.map(
        (c: any) => c.STRING_FIELD,
      )
      expect(STRING_FIELDs).toContain(testConversations[0].STRING_FIELD) // John Doe Conversation
      expect(STRING_FIELDs).toContain(testConversations[1].STRING_FIELD) // Jane Smith Conversation
    })

    it("should handle pagination", async () => {
      const params = createPaginationParams()
      const result = await implHandleGetAllConversations(businessLogic, params)

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(2)
      expect(result.body.data?.total).toBe(4)
    })

    it("should handle sorting by STRING_FIELD", async () => {
      const params = createSortByNameAscParams()
      const result = await implHandleGetAllConversations(businessLogic, params)

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(4)

      const STRING_FIELDs = result.body.data?.items.map(
        (c: any) => c.STRING_FIELD,
      )
      expect(STRING_FIELDs![0]).toBe(testConversations[3].STRING_FIELD) // Alice Brown Conversation
      expect(STRING_FIELDs![1]).toBe(testConversations[2].STRING_FIELD) // Bob Johnson Conversation
      expect(STRING_FIELDs![2]).toBe(testConversations[1].STRING_FIELD) // Jane Smith Conversation
      expect(STRING_FIELDs![3]).toBe(testConversations[0].STRING_FIELD) // John Doe Conversation
    })

    it("should combine search and tag filtering", async () => {
      // This should work if the implementation supports both search and tag filtering
      // For now, tag filtering takes precedence over search in our implementation
      const params = createSearchAndTagParams()
      const result = await implHandleGetAllConversations(businessLogic, params)

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(2) // VIP conversations (tag filter applied)
    })

    it("should return empty results for non-existent search", async () => {
      const params = createNonExistentSearchParams()
      const result = await implHandleGetAllConversations(businessLogic, params)

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(0)
      expect(result.body.data?.total).toBe(0)
    })

    it("should return empty results for non-existent tag", async () => {
      const params = createNonExistentTagParams()
      const result = await implHandleGetAllConversations(businessLogic, params)

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(0)
      expect(result.body.data?.total).toBe(0)
    })

    it("should fail with empty search keyword", async () => {
      const params = createEmptySearchParams()
      const result = await implHandleGetAllConversations(businessLogic, params)

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("Search keyword cannot be empty")
    })

    it("should fail with empty filter field", async () => {
      const params = createEmptyTagParams()
      const result = await implHandleGetAllConversations(businessLogic, params)

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("Filter field cannot be empty")
    })

    it("should handle whitespace-only search", async () => {
      const params = createWhitespaceSearchParams()
      const result = await implHandleGetAllConversations(businessLogic, params)

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("Search keyword cannot be empty")
    })

    it("should handle whitespace-only filter field", async () => {
      const params = createWhitespaceTagParams()
      const result = await implHandleGetAllConversations(businessLogic, params)

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("Filter field cannot be empty")
    })

    it("should include soft deleted conversations when specified", async () => {
      // Get all conversations first to get one to delete
      const allResult = await implHandleGetAllConversations(businessLogic)
      expect(allResult.status).toBe(200)
      const conversationsToDelete = allResult.body.data?.items[0]
      expect(conversationsToDelete).toBeDefined()

      // Soft delete one conversations
      const deleteResult = await implHandleDeleteConversation(
        conversationsToDelete!.id,
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)

      // Get all without including deleted
      const resultWithoutDeleted =
        await implHandleGetAllConversations(businessLogic)
      expect(resultWithoutDeleted.body.data?.items).toHaveLength(3)

      // Get all including deleted
      const params = createIncludeDeletedParams()
      const resultWithDeleted = await implHandleGetAllConversations(
        businessLogic,
        params,
      )
      expect(resultWithDeleted.body.data?.items).toHaveLength(4)
    })
  })
})
