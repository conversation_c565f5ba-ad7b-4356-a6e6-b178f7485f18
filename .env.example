# Base URL
NEXT_PUBLIC_APP_BASE_URL=https://app.project.com
NEXT_PUBLIC_API_BASE_URL=https://ap1-southeast.project.com

# Supported Language
SUPPORTED_LANGUAGES=en,id,ja,ar
DEFAULT_LANGUAGE=id

# WhatsApp/WAHA Configuration
WAHA_API_URL=https://your-waha-instance.waha.my.id
WAHA_API_KEY=your_waha_api_key_here

# GOWA API Configuration
GOWA_API_BASE=https://your-gowa-instance.sumopod.my.id
GOWA_USERNAME=your_gowa_username
GOWA_PASSWORD=your_gowa_password

# WhatsApp Provider Selection
WHATSAPP_PROVIDER=waha
# WHATSAPP_PROVIDER=chatclient

# Pusher/Realtime Configuration
PUSHER_APP_ID=your_pusher_app_id_here
PUSHER_KEY=your_pusher_key_here
PUSHER_SECRET=your_pusher_secret_here
PUSHER_CLUSTER=ap1

# Webhook Configuration
WAHA_WEBHOOK_URL=https://yourdomain.com/api/v1/functions/webhook
INTERNAL_SECRET_TOKEN=your_internal_secret_token_here

# Upstash Redis Configuration
UPSTASH_REDIS_REST_URL=https://your-redis-instance.upstash.io
UPSTASH_REDIS_REST_TOKEN=your_upstash_redis_token_here

# Email Service (Resend)
RESEND_API_KEY=your_resend_api_key_here
EMAIL_DOMAIN_SEND=yourdomain.com

# Database Configuration
MONGODB_URI=mongodb+srv://username:<EMAIL>/database?retryWrites=true&w=majority&appName=YourApp
REDIS_URL=https://your-redis-instance.upstash.io
REDIS_TOKEN=your_redis_token_here

# JWT Configuration
JWT_REFRESH_SECRET=your_jwt_refresh_secret_here
JWT_SECRET=your_jwt_secret_here

# N8N Workflow Configuration
AI_ENGINE_WEBHOOK_URL=https://your-n8n-instance.com/webhook/your-webhook-id
AI_ENGINE_CALLBACK_AI_EXECUTION=https://yourdomain.com/api/v1/ai/execution
AI_ENGINE_CALLBACK_AI_WORKFLOW_EXECUTION=https://yourdomain.com/api/v1/ai-workflow-executions/[id]/steps

# Parser Engine Configuration
PARSER_ENGINE_URL=https://your-n8n-instance.com/webhook/your-parser-webhook-id
PARSER_CALLBACK_URL=https://yourdomain.com/api/v1/knowledge-base/parser-callback

# Pinecone Vector Database
PINECONE_API_KEY=your_pinecone_api_key_here

# Development Only (Chat Client)
CHAT_CLIENT_BASE_URL=http://localhost:5173

# Required for Grafana
# GRAFANA_LOKI_ENDPOINT=https://your-grafana.com
# GRAFANA_API_KEY=your-api-key

# Optional
# debug, info, warn, error, fatal
LOG_LEVEL=info                   
# Enable console hijacking 
OVERRIDE_CONSOLE=true            
# Stack traces in production
ENABLE_STACK_TRACE=false         


# Docker Configuration
DOCKER_EXPOSED_PORT=3002

# Infrastructure
# Message Queue Configuration (RabbitMQ)
RABBITMQ_URL=amqp://admin:admin123@localhost:5672